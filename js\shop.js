import { gameState } from './gameState.js';
import { UPGRADES, GAME_BALANCE, MINERAL_DATA } from './constants.js';
import { updateUI } from './ui.js';
import { showNotification } from './utils.js';

// Shop and Upgrade System

export function buyDrillEnhancement() {
  const cost = getDrillEnhancementCost();

  if (gameState.getCoins() >= cost) {
    gameState.subtractCoins(cost);
    gameState.incrementDrillLevel();

    // Play upgrade sound if enabled
    if (gameState.isSoundEffectsEnabled()) {
      const upgradeSound = document.getElementById('upgrade-sound');
      if (upgradeSound) {
        upgradeSound.currentTime = 0;
        upgradeSound.play().catch(e => console.warn('Could not play upgrade sound:', e));
      }
    }

    updateUI();
    showNotification(`Drill Enhancement upgraded to level ${gameState.getDrillLevel()}! Mining is now ${Math.round(gameState.getDrillLevel() * UPGRADES.DRILL_SPEED_BOOST * 100)}% faster.`, 'success');

    // Achievement check
    if (!gameState.achievements.firstUpgrade) {
      gameState.achievements.firstUpgrade = true;
      showNotification('Achievement: First Upgrade!', 'success');
    }
  } else {
    showNotification(`Need ${cost - gameState.getCoins()} more coins for Drill Enhancement!`, 'warning');
  }
}

export function buyVeinChanceBoost() {
  const cost = getVeinChanceBoostCost();
  const currentVeinChance = gameState.getVeinChance();

  if (currentVeinChance >= 0.8) {
    showNotification('Vein chance is already at maximum (80%)!', 'warning');
    return;
  }

  if (gameState.getCoins() >= cost) {
    gameState.subtractCoins(cost);
    gameState.addVeinChance(0.08); // +8% vein chance

    // Cap at 80%
    if (gameState.getVeinChance() > 0.8) {
      gameState.setVeinChance(0.8);
    }

    // Play upgrade sound if enabled
    if (gameState.isSoundEffectsEnabled()) {
      const upgradeSound = document.getElementById('upgrade-sound');
      if (upgradeSound) {
        upgradeSound.currentTime = 0;
        upgradeSound.play().catch(e => console.warn('Could not play upgrade sound:', e));
      }
    }

    updateUI();
    showNotification(`Vein chance increased to ${Math.round(gameState.getVeinChance() * 100)}%!`, 'success');
  } else {
    showNotification(`Need ${cost - gameState.getCoins()} more coins for Vein Chance Boost!`, 'warning');
  }
}

export function buyInventoryUpgrade() {
  const cost = getInventoryUpgradeCost();

  if (gameState.getCoins() >= cost) {
    gameState.subtractCoins(cost);
    gameState.incrementInventoryUpgradeLevel();

    // Play upgrade sound if enabled
    if (gameState.isSoundEffectsEnabled()) {
      const upgradeSound = document.getElementById('upgrade-sound');
      if (upgradeSound) {
        upgradeSound.currentTime = 0;
        upgradeSound.play().catch(e => console.warn('Could not play upgrade sound:', e));
      }
    }

    updateUI();
    showNotification(`Inventory expanded to ${gameState.getMaxInventorySlots()} slots! (+${UPGRADES.INVENTORY_UPGRADE_SLOTS} slots)`, 'success');
  } else {
    showNotification(`Need ${cost - gameState.getCoins()} more coins for Inventory Expansion!`, 'warning');
  }
}

export function buyAutoSell() {
  const cost = GAME_BALANCE.AUTO_SELL_COST;

  if (gameState.autoSellEnabled) {
    showNotification('Auto-Sell is already enabled!', 'warning');
    return;
  }

  if (gameState.getCurrentTier() <= 1) {
    showNotification('Auto-Sell unlocks at Tier 2+!', 'warning');
    return;
  }

  if (gameState.getCoins() >= cost) {
    gameState.subtractCoins(cost);
    gameState.autoSellEnabled = true;

    // Play upgrade sound if enabled
    if (gameState.isSoundEffectsEnabled()) {
      const upgradeSound = document.getElementById('upgrade-sound');
      if (upgradeSound) {
        upgradeSound.currentTime = 0;
        upgradeSound.play().catch(e => console.warn('Could not play upgrade sound:', e));
      }
    }

    updateUI();
    showNotification('Auto-Sell enabled! Minerals will be automatically sold at 50% value.', 'success');

    // Start auto-sell interval
    startAutoSell();
  } else {
    showNotification(`Need ${cost - gameState.getCoins()} more coins for Auto-Sell!`, 'warning');
  }
}

export function prestigeReset() {
  const cost = GAME_BALANCE.PRESTIGE_REQUIREMENT;

  if (gameState.getCoins() < cost) {
    showNotification(`Need ${cost} coins to prestige!`, 'warning');
    return;
  }

  if (!confirm(`Prestige will reset your progress but give you a permanent 10% bonus to all earnings. Continue?`)) {
    return;
  }

  // Increase prestige bonus
  gameState.prestigeBonus += 0.1;

  // Reset most game state but keep prestige bonus
  const oldPrestigeBonus = gameState.prestigeBonus;
  const oldAchievements = { ...gameState.achievements };
  const oldStats = { ...gameState.gameStats };

  gameState.reset();
  gameState.prestigeBonus = oldPrestigeBonus;
  gameState.achievements = oldAchievements;
  gameState.gameStats = oldStats;

  // Mark prestige achievement
  if (!gameState.achievements.firstPrestige) {
    gameState.achievements.firstPrestige = true;
  }

  updateUI();
  showNotification(`Prestige complete! You now have a ${Math.round((gameState.prestigeBonus - 1) * 100)}% bonus to all earnings!`, 'success');

  // Achievement notification
  if (gameState.achievements.firstPrestige) {
    setTimeout(() => {
      showNotification('Achievement: First Prestige!', 'success');
    }, 1000);
  }
}

// Cost calculation functions
export function getDrillEnhancementCost() {
  return Math.floor(UPGRADES.BASE_DRILL_COST * Math.pow(1.3, gameState.getDrillLevel()));
}

export function getVeinChanceBoostCost() {
  return Math.floor(GAME_BALANCE.BASE_VEIN_BOOST_COST * Math.pow(1.1, gameState.getVeinChance() / 0.08));
}

export function getInventoryUpgradeCost() {
  return Math.floor(UPGRADES.BASE_INVENTORY_UPGRADE_COST * Math.pow(1.4, gameState.getInventoryUpgradeLevel()));
}

// Auto-sell system
let autoSellInterval = null;

export function startAutoSell() {
  if (!gameState.autoSellEnabled || autoSellInterval) return;

  autoSellInterval = setInterval(() => {
    autoSellMinerals();
  }, 10000); // Auto-sell every 10 seconds
}

export function stopAutoSell() {
  if (autoSellInterval) {
    clearInterval(autoSellInterval);
    autoSellInterval = null;
  }
}

function autoSellMinerals() {
  if (!gameState.autoSellEnabled) return;

  const inventory = gameState.getInventory();
  let totalValue = 0;
  let mineralsSold = 0;

  for (const mineralName in inventory) {
    if (inventory[mineralName] > 0) {
      const mineralValue = MINERAL_DATA[mineralName]?.value || 1;
      const amount = inventory[mineralName];
      // Auto-sell at 50% value
      const sellValue = Math.floor(mineralValue * 0.5);
      totalValue += amount * sellValue;
      mineralsSold += amount;
      gameState.removeMineralFromInventory(mineralName, amount);
    }
  }

  if (totalValue > 0) {
    gameState.addCoins(totalValue);
    updateUI();

    // Show notification occasionally (not every time to avoid spam)
    if (Math.random() < 0.1) { // 10% chance
      showNotification(`Auto-sold ${mineralsSold} minerals for ${totalValue} coins (50% value)`, 'success');
    }
  }
}

// Update shop display
export function updateShopDisplay() {
  // Update drill enhancement
  const drillLevelElement = document.getElementById("drill-level");
  const drillCostElement = document.getElementById("drill-cost");
  if (drillLevelElement) drillLevelElement.textContent = gameState.getDrillLevel();
  if (drillCostElement) drillCostElement.textContent = getDrillEnhancementCost();

  // Update vein chance boost
  const veinBoostCostElement = document.getElementById("vein-boost-cost");
  const veinChanceBoostBtn = document.getElementById("veinChanceBoostBtn");
  if (veinBoostCostElement) veinBoostCostElement.textContent = getVeinChanceBoostCost();
  if (veinChanceBoostBtn) {
    veinChanceBoostBtn.disabled = gameState.getVeinChance() >= 0.8;
    if (gameState.getVeinChance() >= 0.8) {
      veinChanceBoostBtn.textContent = "Max Level Reached";
    }
  }

  // Update inventory upgrade
  const inventoryLevelElement = document.getElementById("inventory-level");
  const inventoryCostElement = document.getElementById("inventory-cost");
  if (inventoryLevelElement) inventoryLevelElement.textContent = gameState.getInventoryUpgradeLevel();
  if (inventoryCostElement) inventoryCostElement.textContent = getInventoryUpgradeCost();

  // Update auto-sell
  const autoSellCostElement = document.getElementById("autosell-cost");
  const autoSellBtn = document.getElementById("autosell-btn");
  const autoSellItem = document.getElementById("autosell-shop-item");

  if (autoSellCostElement) autoSellCostElement.textContent = GAME_BALANCE.AUTO_SELL_COST;
  if (autoSellBtn) autoSellBtn.disabled = gameState.autoSellEnabled;
  if (autoSellItem) {
    // Show/hide Auto-Sell based on tier (only show when tier > 1)
    autoSellItem.style.display = gameState.getCurrentTier() > 1 ? "block" : "none";
  }

  // Update prestige button
  const prestigeBtn = document.getElementById("prestige-btn");
  if (prestigeBtn) {
    prestigeBtn.disabled = gameState.getCoins() < GAME_BALANCE.PRESTIGE_REQUIREMENT;
  }
}

// Initialize auto-sell if enabled
export function initializeAutoSell() {
  if (gameState.autoSellEnabled) {
    startAutoSell();
  }
}
