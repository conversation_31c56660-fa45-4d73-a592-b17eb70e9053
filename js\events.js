import { gameState } from './gameState.js';
import { EVENT_TYPES } from './constants.js';
import { showNotification, getRandomElement } from './utils.js';

// Event System Management

export function initializeEventSystem() {
  console.log('Event system initialized');
  startEventTimer();
}

function startEventTimer() {
  // Check for events every 30 seconds
  setInterval(() => {
    checkForRandomEvent();
    updateActiveEvents();
  }, 30000);
}

function checkForRandomEvent() {
  const now = Date.now();
  
  // Don't trigger events if one is already active
  if (gameState.getActiveEvents().length > 0) {
    return;
  }
  
  // Check cooldown (1 minute between events)
  if (now - gameState.lastEventTime < gameState.eventCooldown) {
    return;
  }
  
  // Only trigger events if auto-mining is active and player has progressed
  if (!gameState.isAutoMining() || gameState.getCurrentDepth() < 50) {
    return;
  }
  
  // 15% chance to trigger an event
  if (Math.random() < 0.15) {
    triggerRandomEvent();
  }
}

function triggerRandomEvent() {
  const eventType = getRandomElement(EVENT_TYPES);
  const now = Date.now();
  
  const event = {
    ...eventType,
    startTime: now,
    endTime: now + eventType.duration
  };
  
  gameState.addActiveEvent(event);
  gameState.lastEventTime = now;
  
  // Add to history
  gameState.eventHistory.unshift({
    ...event,
    completed: false
  });
  
  // Keep only last 10 events in history
  if (gameState.eventHistory.length > 10) {
    gameState.eventHistory = gameState.eventHistory.slice(0, 10);
  }
  
  // Show event popup
  showEventPopup(event);
  
  console.log('Event triggered:', event);
}

function showEventPopup(event) {
  // Remove any existing event popup
  const existingPopup = document.getElementById('event-popup');
  if (existingPopup) {
    existingPopup.remove();
  }
  
  // Create event popup
  const popup = document.createElement('div');
  popup.id = 'event-popup';
  popup.className = 'event-popup';
  popup.innerHTML = `
    <div class="event-popup-content">
      <div class="event-header">
        <span class="event-icon">${event.icon}</span>
        <h3>${event.name}</h3>
        <button class="event-close" onclick="this.parentElement.parentElement.parentElement.remove()">×</button>
      </div>
      <p>${event.description}</p>
      <div class="event-duration">
        Duration: ${Math.ceil(event.duration / 1000)} seconds
      </div>
    </div>
  `;
  
  // Style the popup
  popup.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
    border: 2px solid #ff6464;
    border-radius: 12px;
    padding: 20px;
    z-index: 10000;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8);
    color: white;
    animation: eventPopupSlide 0.3s ease-out;
  `;
  
  // Add animation keyframes if not already added
  if (!document.getElementById('event-popup-styles')) {
    const style = document.createElement('style');
    style.id = 'event-popup-styles';
    style.textContent = `
      @keyframes eventPopupSlide {
        from {
          opacity: 0;
          transform: translate(-50%, -60%);
        }
        to {
          opacity: 1;
          transform: translate(-50%, -50%);
        }
      }
      .event-popup-content {
        text-align: center;
      }
      .event-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
      }
      .event-icon {
        font-size: 24px;
      }
      .event-close {
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .event-close:hover {
        background: rgba(255, 255, 255, 0.1);
      }
      .event-duration {
        margin-top: 15px;
        font-size: 14px;
        color: #ccc;
      }
    `;
    document.head.appendChild(style);
  }
  
  document.body.appendChild(popup);
  gameState.currentEventPopup = popup;
  
  // Auto-close after 5 seconds
  setTimeout(() => {
    if (popup.parentNode) {
      popup.remove();
    }
    if (gameState.currentEventPopup === popup) {
      gameState.currentEventPopup = null;
    }
  }, 5000);
  
  // Show notification as well
  showNotification(`${event.icon} ${event.name}: ${event.description}`, 'warning', 4000);
}

function updateActiveEvents() {
  const now = Date.now();
  const activeEvents = gameState.getActiveEvents();
  
  // Remove expired events
  const expiredEvents = activeEvents.filter(event => now >= event.endTime);
  expiredEvents.forEach(event => {
    gameState.removeActiveEvent(event.id);
    
    // Mark as completed in history
    const historyEvent = gameState.eventHistory.find(h => h.id === event.id && h.startTime === event.startTime);
    if (historyEvent) {
      historyEvent.completed = true;
    }
    
    showNotification(`${event.icon} ${event.name} has ended.`, 'success');
    console.log('Event ended:', event);
  });
}

export function getActiveEventEffects() {
  const activeEvents = gameState.getActiveEvents();
  const effects = {
    miningHalted: false,
    miningSlowed: false,
    slowMultiplier: 1
  };
  
  activeEvents.forEach(event => {
    if (event.effect === 'mining_halt') {
      effects.miningHalted = true;
    } else if (event.effect === 'slow_mining') {
      effects.miningSlowed = true;
      effects.slowMultiplier = Math.min(effects.slowMultiplier, event.multiplier);
    }
  });
  
  return effects;
}

export function forceEndEvent(eventId) {
  const activeEvents = gameState.getActiveEvents();
  const event = activeEvents.find(e => e.id === eventId);
  
  if (event) {
    gameState.removeActiveEvent(eventId);
    
    // Mark as completed in history
    const historyEvent = gameState.eventHistory.find(h => h.id === eventId && h.startTime === event.startTime);
    if (historyEvent) {
      historyEvent.completed = true;
    }
    
    showNotification(`${event.icon} ${event.name} was forcefully ended.`, 'success');
    console.log('Event forcefully ended:', event);
    return true;
  }
  
  return false;
}

export function triggerSpecificEvent(eventId) {
  const eventType = EVENT_TYPES.find(e => e.id === eventId);
  
  if (!eventType) {
    console.error('Event type not found:', eventId);
    return false;
  }
  
  const now = Date.now();
  const event = {
    ...eventType,
    startTime: now,
    endTime: now + eventType.duration
  };
  
  gameState.addActiveEvent(event);
  gameState.lastEventTime = now;
  
  // Add to history
  gameState.eventHistory.unshift({
    ...event,
    completed: false
  });
  
  // Show event popup
  showEventPopup(event);
  
  console.log('Specific event triggered:', event);
  return true;
}

export function getEventHistory() {
  return gameState.eventHistory;
}

export function clearEventHistory() {
  gameState.eventHistory = [];
  showNotification('Event history cleared.', 'success');
}

export function getEventStats() {
  const history = gameState.eventHistory;
  const stats = {
    totalEvents: history.length,
    completedEvents: history.filter(e => e.completed).length,
    eventTypes: {}
  };
  
  // Count events by type
  history.forEach(event => {
    if (!stats.eventTypes[event.id]) {
      stats.eventTypes[event.id] = {
        name: event.name,
        count: 0,
        completed: 0
      };
    }
    stats.eventTypes[event.id].count++;
    if (event.completed) {
      stats.eventTypes[event.id].completed++;
    }
  });
  
  return stats;
}

// Debug functions for testing events
export function debugTriggerRandomEvent() {
  if (gameState.getActiveEvents().length > 0) {
    showNotification('An event is already active!', 'warning');
    return;
  }
  
  triggerRandomEvent();
  showNotification('Debug: Random event triggered!', 'success');
}

export function debugEndAllEvents() {
  const activeEvents = [...gameState.getActiveEvents()];
  activeEvents.forEach(event => {
    forceEndEvent(event.id);
  });
  
  if (activeEvents.length > 0) {
    showNotification(`Debug: Ended ${activeEvents.length} active events.`, 'success');
  } else {
    showNotification('Debug: No active events to end.', 'warning');
  }
}

// Event system status
export function getEventSystemStatus() {
  const now = Date.now();
  const timeSinceLastEvent = now - gameState.lastEventTime;
  const timeUntilNextPossible = Math.max(0, gameState.eventCooldown - timeSinceLastEvent);
  
  return {
    activeEvents: gameState.getActiveEvents().length,
    timeSinceLastEvent: timeSinceLastEvent,
    timeUntilNextPossible: timeUntilNextPossible,
    canTriggerEvent: timeUntilNextPossible === 0 && gameState.getActiveEvents().length === 0,
    autoMiningRequired: gameState.isAutoMining(),
    depthRequired: gameState.getCurrentDepth() >= 50
  };
}
