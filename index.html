<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Idle Miner</title>
<style>
  html, body {
    margin: 0;
    height: 100%;
    background-color: #666666;
    font-family: monospace, sans-serif;
    color: #eee;
    user-select: none;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    min-height: 100vh;
    overflow: hidden;
  }
  button {
    margin: 10px 5px;
    padding: 10px 20px;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  button:hover {
    background-color: rgba(0, 0, 0, 0.9);
  }
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  .notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    border: 1px solid #666;
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
    animation: slideIn 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  }
  .notification.success {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.2);
  }
  .notification.warning {
    border-color: #FF9800;
    background: rgba(255, 152, 0, 0.2);
  }
  .notification.error {
    border-color: #f44336;
    background: rgba(244, 67, 54, 0.2);
  }
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  .coin-gain {
    position: fixed;
    color: #ffd700;
    font-weight: bold;
    font-size: 18px;
    pointer-events: none;
    z-index: 1000;
    animation: coinFloat 1s ease-out forwards;
  }
  @keyframes coinFloat {
    0% {
      opacity: 1;
      transform: translateY(0);
    }
    100% {
      opacity: 0;
      transform: translateY(-50px);
    }
  }
  h1, h2, p {
    text-shadow: 2px 2px 5px #000;
    margin: 5px 0 15px 0;
  }
  .ui-container {
    position: relative;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 12px;
    padding: 25px;
    width: 1200px;
    height: 800px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: 1fr 280px;
    grid-template-rows: auto auto 1fr auto;
    grid-gap: 20px;
    box-sizing: border-box;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  /* Display size variants */
  .ui-container.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
  }

  .ui-container.large {
    width: 1200px;
    height: 800px;
    grid-template-columns: 1fr 280px;
    grid-gap: 25px;
  }

  .ui-container.medium {
    width: 900px;
    height: 600px;
    grid-template-columns: 1fr 220px;
    grid-gap: 20px;
  }

  .ui-container.small {
    width: 600px;
    height: 400px;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto 1fr auto;
    grid-gap: 15px;
  }

  .ui-container.corner {
    width: 400px;
    height: 300px;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto 1fr auto;
    grid-gap: 10px;
    padding: 15px;
  }

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    grid-column: 1 / -1;
  }

  .title-area h1 {
    margin: 0;
    font-size: 28px;
    color: #fff;
    text-shadow: 2px 2px 8px #000;
  }

  .stats-area {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
  }

  #coin-display {
    font-weight: bold;
    font-size: 24px;
    user-select: text;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }

  .depth-display {
    font-size: 16px;
    color: #ccc;
  }
  .mining-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
    background: rgba(255, 255, 255, 0.03);
    padding: 30px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 0;
  }

  .progress-section {
    display: flex;
    align-items: center;
    gap: 30px;
    width: 100%;
    justify-content: center;
  }

  .inventory-section {
    background: rgba(255, 255, 255, 0.03);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow-y: auto;
    max-height: 100%;
  }

  .inventory-section h2 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 18px;
    text-align: center;
  }

  .inventory-list {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
    max-height: 300px;
    overflow-y: auto;
  }

  .inventory-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin: 4px 0;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border-left: 4px solid;
    font-size: 14px;
    transition: background-color 0.2s ease;
  }

  .inventory-list li:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .mineral-item {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
  }

  .mineral-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
  }

  .mineral-value {
    color: #4CAF50;
    font-weight: bold;
    margin-right: 8px;
  }

  .sell-all-btn {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    background: linear-gradient(to bottom, #4CAF50, #45a049);
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .sell-all-btn:hover {
    background: linear-gradient(to bottom, #5CBF60, #4CAF50);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
  }

  .bottom-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    grid-column: 1 / -1;
    padding: 10px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .bottom-buttons button {
    flex: 1;
    min-width: 120px;
    padding: 12px 8px;
    font-size: 14px;
    background: linear-gradient(to bottom, #555, #333);
    border: 1px solid #666;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
  }

  .bottom-buttons button:hover {
    background: linear-gradient(to bottom, #666, #444);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  }

  .bottom-buttons button[aria-pressed="true"] {
    background: linear-gradient(to bottom, #4CAF50, #45a049);
    border-color: #4CAF50;
  }

  .panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    display: none;
  }

  .panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
    border: 2px solid #666;
    border-radius: 12px;
    padding: 25px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    z-index: 1001;
    display: none;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8);
  }

  .panel h2 {
    margin: 0 0 20px 0;
    color: #fff;
    font-size: 24px;
    text-align: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  .panel h2:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .shop-item {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .shop-item p {
    margin: 5px 0;
    font-size: 16px;
  }

  .shop-item button {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    background: linear-gradient(to bottom, #4CAF50, #45a049);
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 10px;
  }

  .shop-item button:hover {
    background: linear-gradient(to bottom, #5CBF60, #4CAF50);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
  }

  .shop-item button:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .prestige-item {
    border: 2px solid #FFD700;
    background: rgba(255, 215, 0, 0.1);
  }

  .prestige-item button {
    background: linear-gradient(to bottom, #FFD700, #FFA500);
    color: #000;
    font-weight: bold;
  }

  .prestige-item button:hover {
    background: linear-gradient(to bottom, #FFED4E, #FFD700);
  }

  /* Responsive adjustments for smaller sizes */
  .ui-container.small .header-section,
  .ui-container.corner .header-section {
    flex-direction: column;
    gap: 5px;
    text-align: center;
    padding: 10px;
  }

  .ui-container.small .title-area h1,
  .ui-container.corner .title-area h1 {
    font-size: 18px;
    margin: 0;
  }

  .ui-container.small .stats-area,
  .ui-container.corner .stats-area {
    align-items: center;
  }

  .ui-container.small #coin-display,
  .ui-container.corner #coin-display {
    font-size: 16px;
  }

  .ui-container.small .depth-display,
  .ui-container.corner .depth-display {
    font-size: 12px;
  }

  .ui-container.small .inventory-section h2,
  .ui-container.corner .inventory-section h2 {
    font-size: 14px;
    margin: 0 0 8px 0;
  }

  .ui-container.small .inventory-list li,
  .ui-container.corner .inventory-list li {
    font-size: 12px;
    padding: 4px 8px;
  }

  .ui-container.small .sell-all-btn,
  .ui-container.corner .sell-all-btn {
    padding: 8px;
    font-size: 12px;
  }

  .ui-container.small .bottom-buttons button,
  .ui-container.corner .bottom-buttons button {
    font-size: 10px;
    padding: 6px 4px;
    min-width: 80px;
  }

  .ui-container.small .mining-section,
  .ui-container.corner .mining-section {
    padding: 15px;
    gap: 15px;
  }

  .ui-container.medium .header-section {
    padding: 0 8px;
  }

  .ui-container.medium .title-area h1 {
    font-size: 22px;
  }

  .ui-container.medium #coin-display {
    font-size: 20px;
  }

  .ui-container.medium .inventory-section {
    padding: 15px;
  }

  .ui-container.medium .inventory-section h2 {
    font-size: 16px;
  }

  .ui-container.medium .bottom-buttons button {
    font-size: 12px;
    padding: 8px 6px;
    min-width: 100px;
  }

  .ui-container.medium .mining-section {
    padding: 20px;
    gap: 20px;
  }

  .settings-group {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .settings-group h3 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 18px;
  }

  .size-slider-container {
    margin: 15px 0;
  }

  .size-slider-container label {
    display: block;
    margin-bottom: 10px;
    color: #ccc;
    font-size: 14px;
  }

  .size-slider-container input[type="range"] {
    width: 100%;
    margin: 10px 0;
    background: #333;
    border-radius: 5px;
    outline: none;
    height: 6px;
  }

  .size-slider-container input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  }

  .size-slider-container input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  }

  .slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
    margin-top: 5px;
  }

  /* Dynamic Mining Display */
  .mining-display-container {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .mining-visual {
    width: 300px;
    height: 200px;
    background: linear-gradient(to bottom, #8B4513 0%, #654321 30%, #4A4A4A 60%, #2F2F2F 100%);
    border: 3px solid #666;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
  }

  .mining-chunks {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(15, 1fr);
    grid-template-rows: repeat(10, 1fr);
    gap: 1px;
  }

  .chunk {
    background: rgba(139, 69, 19, 0.8);
    border: 1px solid rgba(101, 67, 33, 0.5);
    transition: all 0.3s ease;
    position: relative;
  }

  .chunk.mined {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.8);
  }

  .chunk.mineral {
    background: radial-gradient(circle, #FFD700, #FFA500);
    border-color: #FFD700;
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
    animation: sparkle 2s infinite;
  }

  .chunk.mineral.mined {
    background: rgba(255, 215, 0, 0.2);
    animation: none;
  }

  @keyframes sparkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .mining-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
    font-size: 16px;
    color: #ccc;
  }

  .depth-info {
    text-align: center;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    border: 1px solid #666;
  }

  .depth-info h3 {
    margin: 0 0 5px 0;
    color: #fff;
    font-size: 18px;
  }

  .depth-info div {
    font-size: 20px;
    font-weight: bold;
    color: #4CAF50;
  }

  .mining-timer {
    text-align: center;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    border: 1px solid #666;
  }

  .progress-stats {
    background: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    border: 1px solid #666;
    padding: 10px 15px;
  }

  .stat-line {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
    font-size: 14px;
  }

  .stat-line span:last-child {
    color: #4CAF50;
    font-weight: bold;
  }

  .timer-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
  }

  #timer-countdown {
    color: #4CAF50;
    font-family: 'Courier New', monospace;
  }

  .mining-timer.halted #timer-countdown {
    color: #ff6b6b;
  }

  .mining-timer.slowed #timer-countdown {
    color: #ffa500;
  }

  .mining-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .mining-controls button {
    padding: 20px 60px;
    font-size: 24px;
    background: linear-gradient(to bottom, #4a6ea9, #3a5a8a);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    transition: all 0.2s ease;
    border-radius: 8px;
    min-width: 250px;
  }

  .mining-controls button:hover {
    background: linear-gradient(to bottom, #5a7eb9, #4a6a9a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
  }

  /* Mobile responsiveness */
  @media (max-width: 900px) {
    .ui-container {
      width: 95%;
      padding: 15px;
      height: 90vh;
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto 1fr auto;
    }

    .header-section {
      flex-direction: column;
      gap: 10px;
      text-align: center;
    }

    .stats-area {
      align-items: center;
    }

    .mining-section {
      order: 1;
    }

    .inventory-section {
      order: 2;
    }

    button {
      padding: 8px 16px;
      font-size: 14px;
    }

    .title-area h1 {
      font-size: 24px;
    }

    .panel {
      padding: 15px;
    }

    .progress-section {
      flex-direction: column;
    }

    .progress-bar {
      width: 25px;
      height: 150px;
    }
  }
</style>
</head>
<body>
  <div class="ui-container" role="main" aria-label="Idle Miner Game UI">
    <!-- Header Section -->
    <div class="header-section">
      <div class="title-area">
        <h1>Idle Miner</h1>
      </div>
      <div class="stats-area">
        <div id="coin-display" aria-live="polite">💰 <span id="coin-count">0</span> Coins</div>
      </div>
    </div>

    <!-- Mining Section -->
    <div class="mining-section">
      <div class="progress-section">
        <div class="mining-display-container">
          <div class="mining-visual">
            <div class="mining-chunks" id="mining-chunks">
              <!-- Chunks will be generated by JavaScript -->
            </div>
          </div>
          <div class="mining-info">
            <div class="depth-info">
              <h3>Current Depth</h3>
              <div id="current-depth">0m</div>
            </div>
            <div class="mining-timer" id="mining-timer">
              <div class="timer-display">
                <span id="timer-text">Next mine in: </span>
                <span id="timer-countdown">--</span>
              </div>
            </div>
            <div class="progress-stats">
              <div class="stat-line">
                <span>Progress:</span>
                <span id="depth-progress-text">0%</span>
              </div>
              <div class="stat-line">
                <span>Next Tier:</span>
                <span id="next-tier">100m</span>
              </div>
              <div class="stat-line">
                <span>Chunks Mined:</span>
                <span id="chunks-mined">0/150</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mining-controls">
        <button onclick="toggleAutoMining()" id="auto-btn" aria-pressed="false">⛏️ Start Mining</button>
      </div>
    </div>

    <!-- Inventory Section -->
    <div class="inventory-section" aria-label="Inventory section">
      <h2>💎 Inventory <span id="inventory-count" style="font-size: 14px; color: #ccc;">(0/8)</span></h2>
      <ul id="inventory" class="inventory-list"></ul>
      <button onclick="sellMinerals()" class="sell-all-btn">💰 Sell All Minerals</button>
    </div>

    <!-- Panel overlay -->
    <div id="panel-overlay" class="panel-overlay" onclick="closeAllPanels()"></div>

    <!-- Shop and Settings panels -->
    <div id="shop-panel" class="panel" aria-hidden="true" aria-label="Shop Section">
      <h2 onclick="closeAllPanels()">🛒 Shop</h2>
      <div class="shop-item">
        <p>⛏️ <strong>Drill Enhancement</strong> (Level <span id="drill-level">0</span>) - 15% faster mining</p>
        <p>Cost: <span id="drill-cost">25</span> coins</p>
        <button onclick="buyDrillEnhancement()">Buy Drill Enhancement</button>
      </div>
      <div class="shop-item">
        <p>💎 <strong>Vein Chance Boost</strong> (+8% Vein Chance)</p>
        <p>Cost: <span id="vein-boost-cost">60</span> coins</p>
        <button onclick="buyVeinChanceBoost()" id="veinChanceBoostBtn">Buy Vein Chance Boost</button>
      </div>
      <div class="shop-item">
        <p>🎒 <strong>Inventory Expansion</strong> (Level <span id="inventory-level">0</span>) - +5 inventory slots</p>
        <p>Cost: <span id="inventory-cost">100</span> coins</p>
        <button onclick="buyInventoryUpgrade()">Buy Inventory Expansion</button>
      </div>
      <div class="shop-item" id="autosell-shop-item" style="display: none;">
        <p>🤖 <strong>Auto-Sell</strong> (Sells minerals at 50% value)</p>
        <p>Cost: <span id="autosell-cost">2000</span> coins</p>
        <button onclick="buyAutoSell()" id="autosell-btn">Buy Auto-Sell</button>
      </div>
      <div class="shop-item prestige-item">
        <p>🚀 <strong>Prestige Reset</strong> (Gain permanent 10% bonus)</p>
        <p>Cost: 5,000 coins</p>
        <button onclick="prestigeReset()" id="prestige-btn">Prestige</button>
      </div>
    </div>

    <div id="settings-panel" class="panel" aria-hidden="true" aria-label="Settings Section">
      <h2 onclick="closeAllPanels()">⚙️ Settings</h2>
      <div class="settings-group">
        <h3>🖥️ Display Settings</h3>
        <div class="size-slider-container">
          <label for="size-slider">Screen Size: <span id="size-display">1200x800</span></label>
          <input type="range" id="size-slider" min="50" max="150" value="100" step="5" onchange="updateScreenSize(this.value)">
          <div class="slider-labels">
            <span>Small</span>
            <span>Medium</span>
            <span>Large</span>
          </div>
        </div>
        <button onclick="toggleFullscreen()" id="fullscreen-btn">🖥️ Toggle Fullscreen</button>
      </div>
      <div class="settings-group">
        <h3>💾 Game Data</h3>
        <button onclick="saveGame()">💾 Save Game</button>
        <button onclick="loadGame()">📁 Load Game</button>
        <button onclick="resetGame()">🔄 Reset Game</button>
      </div>
      <div class="settings-group">
        <h3>🔊 Audio</h3>
        <div style="margin-bottom: 10px;">
          <label for="music-file" style="display: block; margin-bottom: 5px; color: #ccc;">Load Music File (MP3):</label>
          <input type="file" id="music-file" accept="audio/mp3,audio/mpeg" onchange="loadMusicFile(this)" style="width: 100%; margin-bottom: 10px;">
        </div>
        <button onclick="toggleMusic()" id="music-btn">🔈 Music Off</button>
        <button onclick="toggleSoundEffects()" id="sfx-btn">🔉 SFX On</button>
      </div>
    </div>

    <div id="stats-panel" class="panel" aria-hidden="true" aria-label="Game Statistics">
      <h2 onclick="closeAllPanels()">📊 Game Statistics</h2>
      <div id="stats-content"></div>
    </div>

    <div id="encyclopedia-panel" class="panel" aria-hidden="true" aria-label="Mineral Encyclopedia">
      <h2 onclick="closeAllPanels()">📚 Mineral Encyclopedia</h2>

      <div style="margin-bottom: 20px; padding: 15px; background: rgba(255, 255, 255, 0.05); border-radius: 8px; border: 1px solid rgba(255, 255, 255, 0.1);">
        <h3 style="margin-top: 0; color: #fff;">🎮 Visual Mining Simulation</h3>
        <p style="margin: 10px 0; line-height: 1.5;">The mining display shows a 15x10 grid of earth chunks (150 total). Mining progresses <strong>left-to-right, top-to-bottom</strong> in order:</p>
        <ul style="margin: 10px 0; padding-left: 20px; line-height: 1.5;">
          <li><strong>Colored chunks</strong> represent unmined earth (color changes by tier)</li>
          <li><strong>Golden chunks</strong> contain valuable minerals (10% spawn rate)</li>
          <li><strong>Black chunks</strong> have been mined and cleared</li>
          <li><strong>Particle effects</strong> show mining activity</li>
        </ul>
        <p style="margin: 10px 0; line-height: 1.5;"><strong>Tier Colors:</strong> Brown → Gray → Blue → Purple → Red → Orange → Green → Navy → Magenta → Black</p>
        <p style="margin: 10px 0; line-height: 1.5;">When all 150 chunks are mined, the display resets for the next mining tier with new colors and mineral deposits!</p>
      </div>

      <div id="mineral-grid"></div>
    </div>

    <div id="found-minerals-panel" class="panel" aria-hidden="true" aria-label="Found Minerals">
      <h2 onclick="closeAllPanels()">💎 Found Minerals</h2>
      <div id="found-minerals-content">
        <p>Track all the minerals you've discovered during your mining adventures!</p>
        <div id="found-minerals-list"></div>
      </div>
    </div>

    <div id="events-panel" class="panel" aria-hidden="true" aria-label="Current Events">
      <h2 onclick="closeAllPanels()">⚡ Current Events</h2>
      <div id="events-content">
        <p>Random events that affect your mining operations:</p>
        <div id="active-events-list"></div>
        <div id="event-history" style="margin-top: 20px;">
          <h3>Recent Events:</h3>
          <div id="recent-events-list"></div>
        </div>
      </div>
    </div>

    <div id="storage-panel" class="panel" aria-hidden="true" aria-label="Storage Room">
      <h2 onclick="closeAllPanels()">🏺 Storage Room</h2>
      <div id="storage-content">
        <p>Store your valuable artifacts safely here. Artifacts don't take up inventory space when stored.</p>
        <div style="margin: 15px 0;">
          <button onclick="sellAllArtifactsFromStorage()" style="background: linear-gradient(to bottom, #4CAF50, #45a049); padding: 10px 20px;">💰 Sell All Artifacts</button>
        </div>
        <div id="storage-list" style="max-height: 400px; overflow-y: auto;">
          <!-- Storage items will be populated by JavaScript -->
        </div>
      </div>
    </div>

    <div class="bottom-buttons" role="navigation" aria-label="Game options">
      <button id="shop-btn" onclick="toggleShop()" aria-pressed="false" aria-controls="shop-panel">Shop 🛒</button>
      <button id="found-minerals-btn" onclick="toggleFoundMinerals()" aria-pressed="false" aria-controls="found-minerals-panel">Found Minerals 💎</button>
      <button id="storage-btn" onclick="toggleStorage()" aria-pressed="false" aria-controls="storage-panel">Storage 🏺</button>
      <button id="stats-btn" onclick="toggleStats()" aria-pressed="false" aria-controls="stats-panel">Stats 📊</button>
      <button id="events-btn" onclick="toggleEvents()" aria-pressed="false" aria-controls="events-panel">Events ⚡</button>
      <button id="encyclopedia-btn" onclick="toggleEncyclopedia()" aria-pressed="false" aria-controls="encyclopedia-panel">Encyclopedia 📚</button>
      <button id="settings-btn" onclick="toggleSettings()" aria-pressed="false" aria-controls="settings-panel">Settings ⚙️</button>
    </div>
  </div>

  <!-- Audio elements -->
  <audio id="bg-music" loop preload="auto">
    <!-- MP3 file will be loaded dynamically via JavaScript -->
    Your browser does not support the audio element.
  </audio>

  <audio id="mining-sound">
    <source src="https://cdn.pixabay.com/download/audio/2021/08/04/audio_bb630cc098.mp3?filename=mining-pickaxe-hit-180745.mp3" type="audio/mpeg" />
  </audio>

  <audio id="sell-sound">
    <source src="https://cdn.pixabay.com/download/audio/2022/03/15/audio_5a5dc266e5.mp3?filename=coins-handling-193701.mp3" type="audio/mpeg" />
  </audio>

  <audio id="upgrade-sound">
    <source src="https://cdn.pixabay.com/download/audio/2021/08/09/audio_3f6a4a0c3b.mp3?filename=magical-harp-6213.mp3" type="audio/mpeg" />
  </audio>

  <script type="module" src="js/main.js"></script>
</body>
</html>
