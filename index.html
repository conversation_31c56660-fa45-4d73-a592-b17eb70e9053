<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Idle Miner</title>
<style>
  html, body {
    margin: 0;
    height: 100%;
    background-color: #666666;
    font-family: monospace, sans-serif;
    color: #eee;
    user-select: none;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    min-height: 100vh;
    overflow: hidden;
  }
  button {
    margin: 10px 5px;
    padding: 10px 20px;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  button:hover {
    background-color: rgba(0, 0, 0, 0.9);
  }
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  .notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    border: 1px solid #666;
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
    animation: slideIn 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  }
  .notification.success {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.2);
  }
  .notification.warning {
    border-color: #FF9800;
    background: rgba(255, 152, 0, 0.2);
  }
  .notification.error {
    border-color: #f44336;
    background: rgba(244, 67, 54, 0.2);
  }
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  .coin-gain {
    position: fixed;
    color: #ffd700;
    font-weight: bold;
    font-size: 18px;
    pointer-events: none;
    z-index: 1000;
    animation: coinFloat 1s ease-out forwards;
  }
  @keyframes coinFloat {
    0% {
      opacity: 1;
      transform: translateY(0);
    }
    100% {
      opacity: 0;
      transform: translateY(-50px);
    }
  }
  h1, h2, p {
    text-shadow: 2px 2px 5px #000;
    margin: 5px 0 15px 0;
  }
  .ui-container {
    position: relative;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 12px;
    padding: 25px;
    width: 1200px;
    height: 800px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: 1fr 280px;
    grid-template-rows: auto auto 1fr auto;
    grid-gap: 20px;
    box-sizing: border-box;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  /* Display size variants */
  .ui-container.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
  }

  .ui-container.large {
    width: 1200px;
    height: 800px;
    grid-template-columns: 1fr 280px;
    grid-gap: 25px;
  }

  .ui-container.medium {
    width: 900px;
    height: 600px;
    grid-template-columns: 1fr 220px;
    grid-gap: 20px;
  }

  .ui-container.small {
    width: 600px;
    height: 400px;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto 1fr auto;
    grid-gap: 15px;
  }

  .ui-container.corner {
    width: 400px;
    height: 300px;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto 1fr auto;
    grid-gap: 10px;
    padding: 15px;
  }

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    grid-column: 1 / -1;
  }

  .title-area h1 {
    margin: 0;
    font-size: 28px;
    color: #fff;
    text-shadow: 2px 2px 8px #000;
  }

  .stats-area {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
  }

  #coin-display {
    font-weight: bold;
    font-size: 24px;
    user-select: text;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }

  .depth-display {
    font-size: 16px;
    color: #ccc;
  }
  .mining-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
    background: rgba(255, 255, 255, 0.03);
    padding: 30px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 0;
  }

  .progress-section {
    display: flex;
    align-items: center;
    gap: 30px;
    width: 100%;
    justify-content: center;
  }

  .inventory-section {
    background: rgba(255, 255, 255, 0.03);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow-y: auto;
    max-height: 100%;
  }

  .inventory-section h2 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 18px;
    text-align: center;
  }

  .inventory-list {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
    max-height: 300px;
    overflow-y: auto;
  }

  .inventory-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin: 4px 0;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border-left: 4px solid;
    font-size: 14px;
    transition: background-color 0.2s ease;
  }

  .inventory-list li:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .mineral-item {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
  }

  .mineral-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
  }

  .mineral-value {
    color: #4CAF50;
    font-weight: bold;
    margin-right: 8px;
  }

  .sell-all-btn {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    background: linear-gradient(to bottom, #4CAF50, #45a049);
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .sell-all-btn:hover {
    background: linear-gradient(to bottom, #5CBF60, #4CAF50);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
  }

  .bottom-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    grid-column: 1 / -1;
    padding: 10px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .bottom-buttons button {
    flex: 1;
    min-width: 120px;
    padding: 12px 8px;
    font-size: 14px;
    background: linear-gradient(to bottom, #555, #333);
    border: 1px solid #666;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
  }

  .bottom-buttons button:hover {
    background: linear-gradient(to bottom, #666, #444);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  }

  .bottom-buttons button[aria-pressed="true"] {
    background: linear-gradient(to bottom, #4CAF50, #45a049);
    border-color: #4CAF50;
  }

  .panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    display: none;
  }

  .panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
    border: 2px solid #666;
    border-radius: 12px;
    padding: 25px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    z-index: 1001;
    display: none;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8);
  }

  .panel h2 {
    margin: 0 0 20px 0;
    color: #fff;
    font-size: 24px;
    text-align: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  .panel h2:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .shop-item {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .shop-item p {
    margin: 5px 0;
    font-size: 16px;
  }

  .shop-item button {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    background: linear-gradient(to bottom, #4CAF50, #45a049);
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 10px;
  }

  .shop-item button:hover {
    background: linear-gradient(to bottom, #5CBF60, #4CAF50);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
  }

  .shop-item button:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .prestige-item {
    border: 2px solid #FFD700;
    background: rgba(255, 215, 0, 0.1);
  }

  .prestige-item button {
    background: linear-gradient(to bottom, #FFD700, #FFA500);
    color: #000;
    font-weight: bold;
  }

  .prestige-item button:hover {
    background: linear-gradient(to bottom, #FFED4E, #FFD700);
  }

  /* Responsive adjustments for smaller sizes */
  .ui-container.small .header-section,
  .ui-container.corner .header-section {
    flex-direction: column;
    gap: 5px;
    text-align: center;
    padding: 10px;
  }

  .ui-container.small .title-area h1,
  .ui-container.corner .title-area h1 {
    font-size: 18px;
    margin: 0;
  }

  .ui-container.small .stats-area,
  .ui-container.corner .stats-area {
    align-items: center;
  }

  .ui-container.small #coin-display,
  .ui-container.corner #coin-display {
    font-size: 16px;
  }

  .ui-container.small .depth-display,
  .ui-container.corner .depth-display {
    font-size: 12px;
  }

  .ui-container.small .inventory-section h2,
  .ui-container.corner .inventory-section h2 {
    font-size: 14px;
    margin: 0 0 8px 0;
  }

  .ui-container.small .inventory-list li,
  .ui-container.corner .inventory-list li {
    font-size: 12px;
    padding: 4px 8px;
  }

  .ui-container.small .sell-all-btn,
  .ui-container.corner .sell-all-btn {
    padding: 8px;
    font-size: 12px;
  }

  .ui-container.small .bottom-buttons button,
  .ui-container.corner .bottom-buttons button {
    font-size: 10px;
    padding: 6px 4px;
    min-width: 80px;
  }

  .ui-container.small .mining-section,
  .ui-container.corner .mining-section {
    padding: 15px;
    gap: 15px;
  }

  .ui-container.medium .header-section {
    padding: 0 8px;
  }

  .ui-container.medium .title-area h1 {
    font-size: 22px;
  }

  .ui-container.medium #coin-display {
    font-size: 20px;
  }

  .ui-container.medium .inventory-section {
    padding: 15px;
  }

  .ui-container.medium .inventory-section h2 {
    font-size: 16px;
  }

  .ui-container.medium .bottom-buttons button {
    font-size: 12px;
    padding: 8px 6px;
    min-width: 100px;
  }

  .ui-container.medium .mining-section {
    padding: 20px;
    gap: 20px;
  }

  .settings-group {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .settings-group h3 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 18px;
  }

  .size-slider-container {
    margin: 15px 0;
  }

  .size-slider-container label {
    display: block;
    margin-bottom: 10px;
    color: #ccc;
    font-size: 14px;
  }

  .size-slider-container input[type="range"] {
    width: 100%;
    margin: 10px 0;
    background: #333;
    border-radius: 5px;
    outline: none;
    height: 6px;
  }

  .size-slider-container input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  }

  .size-slider-container input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  }

  .slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
    margin-top: 5px;
  }

  /* Dynamic Mining Display */
  .mining-display-container {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .mining-visual {
    width: 300px;
    height: 200px;
    background: linear-gradient(to bottom, #8B4513 0%, #654321 30%, #4A4A4A 60%, #2F2F2F 100%);
    border: 3px solid #666;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
  }

  .mining-chunks {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(15, 1fr);
    grid-template-rows: repeat(10, 1fr);
    gap: 1px;
  }

  .chunk {
    background: rgba(139, 69, 19, 0.8);
    border: 1px solid rgba(101, 67, 33, 0.5);
    transition: all 0.3s ease;
    position: relative;
  }

  .chunk.mined {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.8);
  }

  .chunk.mineral {
    background: radial-gradient(circle, #FFD700, #FFA500);
    border-color: #FFD700;
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
    animation: sparkle 2s infinite;
  }

  .chunk.mineral.mined {
    background: rgba(255, 215, 0, 0.2);
    animation: none;
  }

  @keyframes sparkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .mining-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
    font-size: 16px;
    color: #ccc;
  }

  .depth-info {
    text-align: center;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    border: 1px solid #666;
  }

  .depth-info h3 {
    margin: 0 0 5px 0;
    color: #fff;
    font-size: 18px;
  }

  .depth-info div {
    font-size: 20px;
    font-weight: bold;
    color: #4CAF50;
  }

  .mining-timer {
    text-align: center;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    border: 1px solid #666;
  }

  .progress-stats {
    background: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    border: 1px solid #666;
    padding: 10px 15px;
  }

  .stat-line {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
    font-size: 14px;
  }

  .stat-line span:last-child {
    color: #4CAF50;
    font-weight: bold;
  }

  .timer-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
  }

  #timer-countdown {
    color: #4CAF50;
    font-family: 'Courier New', monospace;
  }

  .mining-timer.halted #timer-countdown {
    color: #ff6b6b;
  }

  .mining-timer.slowed #timer-countdown {
    color: #ffa500;
  }

  .mining-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .mining-controls button {
    padding: 20px 60px;
    font-size: 24px;
    background: linear-gradient(to bottom, #4a6ea9, #3a5a8a);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    transition: all 0.2s ease;
    border-radius: 8px;
    min-width: 250px;
  }

  .mining-controls button:hover {
    background: linear-gradient(to bottom, #5a7eb9, #4a6a9a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
  }

  /* Mobile responsiveness */
  @media (max-width: 900px) {
    .ui-container {
      width: 95%;
      padding: 15px;
      height: 90vh;
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto 1fr auto;
    }

    .header-section {
      flex-direction: column;
      gap: 10px;
      text-align: center;
    }

    .stats-area {
      align-items: center;
    }

    .mining-section {
      order: 1;
    }

    .inventory-section {
      order: 2;
    }

    button {
      padding: 8px 16px;
      font-size: 14px;
    }

    .title-area h1 {
      font-size: 24px;
    }

    .panel {
      padding: 15px;
    }

    .progress-section {
      flex-direction: column;
    }

    .progress-bar {
      width: 25px;
      height: 150px;
    }
  }
</style>
</head>
<body>
  <div class="ui-container" role="main" aria-label="Idle Miner Game UI">
    <!-- Header Section -->
    <div class="header-section">
      <div class="title-area">
        <h1>Idle Miner</h1>
      </div>
      <div class="stats-area">
        <div id="coin-display" aria-live="polite">💰 <span id="coin-count">0</span> Coins</div>
      </div>
    </div>

    <!-- Mining Section -->
    <div class="mining-section">
      <div class="progress-section">
        <div class="mining-display-container">
          <div class="mining-visual">
            <div class="mining-chunks" id="mining-chunks">
              <!-- Chunks will be generated by JavaScript -->
            </div>
          </div>
          <div class="mining-info">
            <div class="depth-info">
              <h3>Current Depth</h3>
              <div id="current-depth">0m</div>
            </div>
            <div class="mining-timer" id="mining-timer">
              <div class="timer-display">
                <span id="timer-text">Next mine in: </span>
                <span id="timer-countdown">--</span>
              </div>
            </div>
            <div class="progress-stats">
              <div class="stat-line">
                <span>Progress:</span>
                <span id="depth-progress-text">0%</span>
              </div>
              <div class="stat-line">
                <span>Next Tier:</span>
                <span id="next-tier">100m</span>
              </div>
              <div class="stat-line">
                <span>Chunks Mined:</span>
                <span id="chunks-mined">0/150</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mining-controls">
        <button onclick="toggleAutoMining()" id="auto-btn" aria-pressed="false">⛏️ Start Mining</button>
      </div>
    </div>

    <!-- Inventory Section -->
    <div class="inventory-section" aria-label="Inventory section">
      <h2>💎 Inventory <span id="inventory-count" style="font-size: 14px; color: #ccc;">(0/8)</span></h2>
      <ul id="inventory" class="inventory-list"></ul>
      <button onclick="sellMinerals()" class="sell-all-btn">💰 Sell All Minerals</button>
    </div>

    <!-- Panel overlay -->
    <div id="panel-overlay" class="panel-overlay" onclick="closeAllPanels()"></div>

    <!-- Shop and Settings panels -->
    <div id="shop-panel" class="panel" aria-hidden="true" aria-label="Shop Section">
      <h2 onclick="closeAllPanels()">🛒 Shop</h2>
      <div class="shop-item">
        <p>⛏️ <strong>Drill Enhancement</strong> (Level <span id="drill-level">0</span>) - 15% faster mining</p>
        <p>Cost: <span id="drill-cost">25</span> coins</p>
        <button onclick="buyDrillEnhancement()">Buy Drill Enhancement</button>
      </div>
      <div class="shop-item">
        <p>💎 <strong>Vein Chance Boost</strong> (+8% Vein Chance)</p>
        <p>Cost: <span id="vein-boost-cost">60</span> coins</p>
        <button onclick="buyVeinChanceBoost()" id="veinChanceBoostBtn">Buy Vein Chance Boost</button>
      </div>
      <div class="shop-item">
        <p>🎒 <strong>Inventory Expansion</strong> (Level <span id="inventory-level">0</span>) - +5 inventory slots</p>
        <p>Cost: <span id="inventory-cost">100</span> coins</p>
        <button onclick="buyInventoryUpgrade()">Buy Inventory Expansion</button>
      </div>
      <div class="shop-item" id="autosell-shop-item" style="display: none;">
        <p>🤖 <strong>Auto-Sell</strong> (Sells minerals at 50% value)</p>
        <p>Cost: <span id="autosell-cost">2000</span> coins</p>
        <button onclick="buyAutoSell()" id="autosell-btn">Buy Auto-Sell</button>
      </div>
      <div class="shop-item prestige-item">
        <p>🚀 <strong>Prestige Reset</strong> (Gain permanent 10% bonus)</p>
        <p>Cost: 5,000 coins</p>
        <button onclick="prestigeReset()" id="prestige-btn">Prestige</button>
      </div>
    </div>

    <div id="settings-panel" class="panel" aria-hidden="true" aria-label="Settings Section">
      <h2 onclick="closeAllPanels()">⚙️ Settings</h2>
      <div class="settings-group">
        <h3>🖥️ Display Settings</h3>
        <div class="size-slider-container">
          <label for="size-slider">Screen Size: <span id="size-display">1200x800</span></label>
          <input type="range" id="size-slider" min="50" max="150" value="100" step="5" onchange="updateScreenSize(this.value)">
          <div class="slider-labels">
            <span>Small</span>
            <span>Medium</span>
            <span>Large</span>
          </div>
        </div>
        <button onclick="toggleFullscreen()" id="fullscreen-btn">🖥️ Toggle Fullscreen</button>
      </div>
      <div class="settings-group">
        <h3>💾 Game Data</h3>
        <button onclick="saveGame()">💾 Save Game</button>
        <button onclick="loadGame()">📁 Load Game</button>
        <button onclick="resetGame()">🔄 Reset Game</button>
      </div>
      <div class="settings-group">
        <h3>🔊 Audio</h3>
        <div style="margin-bottom: 10px;">
          <label for="music-file" style="display: block; margin-bottom: 5px; color: #ccc;">Load Music File (MP3):</label>
          <input type="file" id="music-file" accept="audio/mp3,audio/mpeg" onchange="loadMusicFile(this)" style="width: 100%; margin-bottom: 10px;">
        </div>
        <button onclick="toggleMusic()" id="music-btn">🔈 Music Off</button>
        <button onclick="toggleSoundEffects()" id="sfx-btn">🔉 SFX On</button>
      </div>
    </div>

    <div id="stats-panel" class="panel" aria-hidden="true" aria-label="Game Statistics">
      <h2 onclick="closeAllPanels()">📊 Game Statistics</h2>
      <div id="stats-content"></div>
    </div>

    <div id="encyclopedia-panel" class="panel" aria-hidden="true" aria-label="Mineral Encyclopedia">
      <h2 onclick="closeAllPanels()">📚 Mineral Encyclopedia</h2>

      <div style="margin-bottom: 20px; padding: 15px; background: rgba(255, 255, 255, 0.05); border-radius: 8px; border: 1px solid rgba(255, 255, 255, 0.1);">
        <h3 style="margin-top: 0; color: #fff;">🎮 Visual Mining Simulation</h3>
        <p style="margin: 10px 0; line-height: 1.5;">The mining display shows a 15x10 grid of earth chunks (150 total). Mining progresses <strong>left-to-right, top-to-bottom</strong> in order:</p>
        <ul style="margin: 10px 0; padding-left: 20px; line-height: 1.5;">
          <li><strong>Colored chunks</strong> represent unmined earth (color changes by tier)</li>
          <li><strong>Golden chunks</strong> contain valuable minerals (10% spawn rate)</li>
          <li><strong>Black chunks</strong> have been mined and cleared</li>
          <li><strong>Particle effects</strong> show mining activity</li>
        </ul>
        <p style="margin: 10px 0; line-height: 1.5;"><strong>Tier Colors:</strong> Brown → Gray → Blue → Purple → Red → Orange → Green → Navy → Magenta → Black</p>
        <p style="margin: 10px 0; line-height: 1.5;">When all 150 chunks are mined, the display resets for the next mining tier with new colors and mineral deposits!</p>
      </div>

      <div id="mineral-grid"></div>
    </div>

    <div id="found-minerals-panel" class="panel" aria-hidden="true" aria-label="Found Minerals">
      <h2 onclick="closeAllPanels()">💎 Found Minerals</h2>
      <div id="found-minerals-content">
        <p>Track all the minerals you've discovered during your mining adventures!</p>
        <div id="found-minerals-list"></div>
      </div>
    </div>

    <div id="events-panel" class="panel" aria-hidden="true" aria-label="Current Events">
      <h2 onclick="closeAllPanels()">⚡ Current Events</h2>
      <div id="events-content">
        <p>Random events that affect your mining operations:</p>
        <div id="active-events-list"></div>
        <div id="event-history" style="margin-top: 20px;">
          <h3>Recent Events:</h3>
          <div id="recent-events-list"></div>
        </div>
      </div>
    </div>

    <div class="bottom-buttons" role="navigation" aria-label="Game options">
      <button id="shop-btn" onclick="toggleShop()" aria-pressed="false" aria-controls="shop-panel">Shop 🛒</button>
      <button id="found-minerals-btn" onclick="toggleFoundMinerals()" aria-pressed="false" aria-controls="found-minerals-panel">Found Minerals 💎</button>
      <button id="stats-btn" onclick="toggleStats()" aria-pressed="false" aria-controls="stats-panel">Stats 📊</button>
      <button id="events-btn" onclick="toggleEvents()" aria-pressed="false" aria-controls="events-panel">Events ⚡</button>
      <button id="encyclopedia-btn" onclick="toggleEncyclopedia()" aria-pressed="false" aria-controls="encyclopedia-panel">Encyclopedia 📚</button>
      <button id="settings-btn" onclick="toggleSettings()" aria-pressed="false" aria-controls="settings-panel">Settings ⚙️</button>
    </div>
  </div>

  <!-- Audio elements -->
  <audio id="bg-music" loop preload="auto">
    <!-- MP3 file will be loaded dynamically via JavaScript -->
    Your browser does not support the audio element.
  </audio>

  <audio id="mining-sound">
    <source src="https://cdn.pixabay.com/download/audio/2021/08/04/audio_bb630cc098.mp3?filename=mining-pickaxe-hit-180745.mp3" type="audio/mpeg" />
  </audio>

  <audio id="sell-sound">
    <source src="https://cdn.pixabay.com/download/audio/2022/03/15/audio_5a5dc266e5.mp3?filename=coins-handling-193701.mp3" type="audio/mpeg" />
  </audio>

  <audio id="upgrade-sound">
    <source src="https://cdn.pixabay.com/download/audio/2021/08/09/audio_3f6a4a0c3b.mp3?filename=magical-harp-6213.mp3" type="audio/mpeg" />
  </audio>

  <script>
    // Game State
    let coins = 0;
    let miningSpeed = 1;
    let currentDepth = 0;
    let depthProgress = 0;
    let currentTier = 0;
    let autoMining = false;
    let autoInterval = null;
    let inventory = {};
    let veinChance = 0;
    let veinChanceBoostCooldown = false;
    let prestigeBonus = 1;
    let totalEarnedCoins = 0;
    let autoSellEnabled = false;
    let mineralsDiscovered = new Set();
    let lastSaveTime = Date.now();
    let soundEffectsEnabled = true;
    let musicEnabled = false;
    let currentScreenScale = 100;
    let isFullscreen = false;

    // Mining display variables
    let miningChunks = [];
    let chunksMinedCount = 0;
    let totalChunks = 150; // 15x10 grid
    let currentMiningIndex = 0; // Track current mining position for left-to-right

    // New upgrade levels
    let drillLevel = 0;
    let inventoryUpgradeLevel = 0;

    // Inventory system
    let maxInventorySlots = 8; // Base inventory limit
    const BASE_INVENTORY_SLOTS = 8;
    const INVENTORY_UPGRADE_SLOTS = 5; // +5 slots per upgrade
    const BASE_INVENTORY_UPGRADE_COST = 100;

    // Found minerals tracking
    let foundMinerals = {}; // Track count of each mineral type found

    // Event system
    let activeEvents = [];
    let eventHistory = [];
    let lastEventTime = 0;
    let eventCooldown = 60000; // 1 minute between events
    let currentEventPopup = null; // Track current event popup

    // Timer system
    let miningTimer = null;
    let nextMineTime = 0;
    let timerUpdateInterval = null;

    // Notification throttling
    let lastInventoryWarning = 0;
    let lastInventoryFullWarning = 0;

    // Tier colors for chunks (excluding gold mineral chunks)
    const tierColors = [
      'rgba(139, 69, 19, 0.8)',    // Tier 0: Brown
      'rgba(105, 105, 105, 0.8)',  // Tier 1: Gray
      'rgba(70, 130, 180, 0.8)',   // Tier 2: Steel Blue
      'rgba(128, 0, 128, 0.8)',    // Tier 3: Purple
      'rgba(220, 20, 60, 0.8)',    // Tier 4: Crimson
      'rgba(255, 140, 0, 0.8)',    // Tier 5: Dark Orange
      'rgba(0, 100, 0, 0.8)',      // Tier 6: Dark Green
      'rgba(25, 25, 112, 0.8)',    // Tier 7: Midnight Blue
      'rgba(139, 0, 139, 0.8)',    // Tier 8: Dark Magenta
      'rgba(0, 0, 0, 0.9)'         // Tier 9: Black
    ];

    // Achievements
    let achievements = {
      firstMineral: false,
      firstVein: false,
      firstUpgrade: false,
      firstSale: false,
      firstPrestige: false
    };

    // Game Statistics
    let gameStats = {
      totalMineralsMined: 0,
      totalVeinsFound: 0,
      timePlayed: 0,
      highestValueMineral: "",
      deepestDepth: 0,
      veinSuccessRate: 0,
      largestVeinFound: 0,
      totalVeinMinerals: 0,
      averageVeinSize: 0
    };

    // Game Balance Constants
    const BASE_MINING_TIME = 30000; // 30 seconds base mining time
    const TIER_TIME_INCREASE = 15000; // +15 seconds per tier
    const BASE_UPGRADE_COST = 25;
    const BASE_VEIN_BOOST_COST = 60;
    const MINING_SPEED_INCREASE = 0.15; // Smaller increments
    const PRESTIGE_REQUIREMENT = 5000;
    const AUTO_SELL_COST = 2000;
    const DEPTH_PER_MINE = 1;
    const DEPTH_TIERS = [100, 300, 600, 1000, 1500, 2100, 2800, 3600, 4500, 5500];

    // New upgrade costs and effects
    const BASE_DRILL_COST = 25;
    const BASE_AUTO_SELL_MINERALS_COST = 500;
    const DRILL_SPEED_BOOST = 0.15; // 15% speed increase per level

    // Event definitions
    const EVENT_TYPES = [
      {
        id: 'broken_drill',
        name: 'Broken Drill Bit',
        description: 'Your drill bit has broken! Mining is halted while repairs are made.',
        icon: '🔧',
        duration: 45000, // 45 seconds
        effect: 'mining_halt'
      },
      {
        id: 'cave_in',
        name: 'Mine Cave-In',
        description: 'A section of the mine has collapsed! Clearing debris takes time.',
        icon: '⛰️',
        duration: 60000, // 60 seconds
        effect: 'mining_halt'
      },
      {
        id: 'equipment_malfunction',
        name: 'Equipment Malfunction',
        description: 'Mining equipment is malfunctioning, reducing efficiency.',
        icon: '⚙️',
        duration: 90000, // 90 seconds
        effect: 'slow_mining',
        multiplier: 0.5 // 50% slower
      },
      {
        id: 'power_outage',
        name: 'Power Outage',
        description: 'Electrical systems are down, severely impacting operations.',
        icon: '⚡',
        duration: 75000, // 75 seconds
        effect: 'slow_mining',
        multiplier: 0.3 // 70% slower
      },
      {
        id: 'gas_leak',
        name: 'Gas Leak',
        description: 'Dangerous gases detected! Mining suspended for safety.',
        icon: '☠️',
        duration: 50000, // 50 seconds
        effect: 'mining_halt'
      }
    ];

    // Mineral tiers and colors
    const mineralData = {
      "Feldspar": { value: 2, color: "#bdb76b" },
      "Gypsum": { value: 2, color: "#dcdcdc" },
      "Quartz": { value: 3, color: "#f0e68c" },
      "Mica": { value: 4, color: "#eee8aa" },
      "Calcite": { value: 5, color: "#ffebcd" },
      "Fluorite": { value: 6, color: "#98fb98" },
      "Magnetite": { value: 7, color: "#778899" },
      "Hematite": { value: 8, color: "#b22222" },
      "Dolomite": { value: 10, color: "#d3d3d3" },
      "Apatite": { value: 12, color: "#7fffd4" },
      "Barite": { value: 13, color: "#a9a9a9" },
      "Talc": { value: 14, color: "#f5f5f5" },
      "Bauxite": { value: 15, color: "#cd853f" },
      "Sphalerite": { value: 18, color: "#d2b48c" },
      "Chalcopyrite": { value: 20, color: "#b8860b" },
      "Galena": { value: 25, color: "#a9a9a9" },
      "Silver": { value: 40, color: "#c0c0c0" },
      "Topaz": { value: 45, color: "#ffd700" },
      "Gold": { value: 50, color: "#ffd700" },
      "Sapphire": { value: 60, color: "#4682b4" },
      "Emerald": { value: 70, color: "#50c878" },
      "Ruby": { value: 80, color: "#e0115f" },
      "Diamond": { value: 100, color: "#b9f2ff" },
      "Platinum": { value: 120, color: "#e5e4e2" },
      "Darkstone": { value: 150, color: "#343434" },
      "Emberglass": { value: 180, color: "#ff4500" },
      "Froststeel": { value: 220, color: "#add8e6" },
      "Starcrystal": { value: 270, color: "#87ceeb" },
      "Sunshard": { value: 320, color: "#ffd700" },
      "Voidgem": { value: 400, color: "#4b0082" }
    };

    // Mineral tiers based on depth
    const mineralTiers = [
      [ // Tier 0 - Surface (0-100m)
        "Feldspar", "Gypsum", "Quartz", "Mica",
        "Calcite", "Fluorite", "Magnetite", "Hematite"
      ],
      [ // Tier 1 - Shallow (100-300m)
        "Dolomite", "Apatite", "Barite", "Talc",
        "Bauxite", "Sphalerite", "Chalcopyrite", "Galena"
      ],
      [ // Tier 2 - Medium (300-600m)
        "Silver", "Topaz", "Gold", "Sapphire",
        "Emerald", "Ruby", "Diamond", "Platinum"
      ],
      [ // Tier 3 - Deep (600-1000m)
        "Darkstone", "Emberglass", "Froststeel",
        "Starcrystal", "Sunshard", "Voidgem"
      ],
      // Additional tiers for deeper levels (same minerals but higher chance of veins)
      [ // Tier 4 (1000-1500m)
        "Silver", "Topaz", "Gold", "Sapphire",
        "Emerald", "Ruby", "Diamond", "Platinum",
        "Darkstone", "Emberglass", "Froststeel",
        "Starcrystal", "Sunshard", "Voidgem"
      ],
      [ // Tier 5 (1500-2100m)
        "Gold", "Sapphire", "Emerald", "Ruby",
        "Diamond", "Platinum", "Darkstone", "Emberglass",
        "Froststeel", "Starcrystal", "Sunshard", "Voidgem"
      ],
      [ // Tier 6 (2100-2800m)
        "Diamond", "Platinum", "Darkstone", "Emberglass",
        "Froststeel", "Starcrystal", "Sunshard", "Voidgem"
      ],
      [ // Tier 7 (2800-3600m)
        "Darkstone", "Emberglass", "Froststeel",
        "Starcrystal", "Sunshard", "Voidgem"
      ],
      [ // Tier 8 (3600-4500m)
        "Emberglass", "Froststeel", "Starcrystal",
        "Sunshard", "Voidgem"
      ],
      [ // Tier 9 (4500-5500m)
        "Starcrystal", "Sunshard", "Voidgem"
      ],
      [ // Tier 10 (5500m+)
        "Voidgem"
      ]
    ];

    // Audio elements
    const bgMusic = document.getElementById("bg-music");
    const miningSound = document.getElementById("mining-sound");
    const sellSound = document.getElementById("sell-sound");
    const upgradeSound = document.getElementById("upgrade-sound");

    // Load music file from user input
    function loadMusicFile(input) {
      const file = input.files[0];
      if (!file) return;

      console.log('Loading music file:', file.name);

      if (!file.type.startsWith('audio/')) {
        alert('Please select an audio file (MP3)');
        return;
      }

      // Create object URL for the file
      const url = URL.createObjectURL(file);
      console.log('Created object URL:', url);

      // Set the audio source
      bgMusic.src = url;
      bgMusic.load();

      console.log('✅ Music file loaded successfully:', file.name);
      alert('Music file loaded! You can now enable music.');

      // Clean up previous URL if exists
      if (bgMusic.previousObjectURL) {
        URL.revokeObjectURL(bgMusic.previousObjectURL);
      }
      bgMusic.previousObjectURL = url;
    }

    // Try to load the default music file
    function tryLoadDefaultMusic() {
      console.log('Attempting to load default music file...');

      // Try different possible paths
      const possiblePaths = [
        'light-contours-ai-213696.mp3',
        './light-contours-ai-213696.mp3',
        '../light-contours-ai-213696.mp3'
      ];

      let pathIndex = 0;

      function tryNextPath() {
        if (pathIndex >= possiblePaths.length) {
          console.log('❌ Default music file not found. Please use the file input to load your MP3.');
          return;
        }

        const path = possiblePaths[pathIndex];
        console.log('Trying path:', path);

        bgMusic.src = path;

        // Set up one-time event listeners
        const onLoad = () => {
          console.log('✅ Default music loaded from:', path);
          bgMusic.removeEventListener('canplaythrough', onLoad);
          bgMusic.removeEventListener('error', onError);
        };

        const onError = () => {
          console.log('❌ Failed to load from:', path);
          bgMusic.removeEventListener('canplaythrough', onLoad);
          bgMusic.removeEventListener('error', onError);
          pathIndex++;
          tryNextPath();
        };

        bgMusic.addEventListener('canplaythrough', onLoad, { once: true });
        bgMusic.addEventListener('error', onError, { once: true });

        bgMusic.load();
      }

      tryNextPath();
    }

    // Set up background music with proper looping
    function initializeMusic() {
      console.log('Initializing music system...');
      console.log('bgMusic element:', bgMusic);

      if (!bgMusic) {
        console.error('❌ bgMusic element not found!');
        return;
      }

      // Set volume to a comfortable level
      bgMusic.volume = 0.4;
      console.log('Set volume to:', bgMusic.volume);

      // Ensure looping is enabled
      bgMusic.loop = true;
      console.log('Loop enabled:', bgMusic.loop);

      // Try to load the default MP3 file if it exists
      tryLoadDefaultMusic();

      // Handle music loading errors gracefully
      bgMusic.addEventListener('error', function(e) {
        console.error('❌ Background music failed to load:', e);
        console.error('Error details:', e.target.error);
        console.error('Network state:', bgMusic.networkState);
        console.error('Ready state:', bgMusic.readyState);

        // Try to provide helpful error message
        if (e.target.error) {
          switch(e.target.error.code) {
            case e.target.error.MEDIA_ERR_ABORTED:
              console.error('Audio loading was aborted');
              break;
            case e.target.error.MEDIA_ERR_NETWORK:
              console.error('Network error while loading audio');
              break;
            case e.target.error.MEDIA_ERR_DECODE:
              console.error('Audio file is corrupted or unsupported format');
              break;
            case e.target.error.MEDIA_ERR_SRC_NOT_SUPPORTED:
              console.error('Audio file format not supported');
              break;
          }
        }
      });

      // Handle successful loading
      bgMusic.addEventListener('canplaythrough', function() {
        console.log('✅ Background music loaded successfully and ready to loop');
        console.log('Music duration:', bgMusic.duration, 'seconds');
      });

      // Backup looping mechanism in case HTML loop attribute fails
      bgMusic.addEventListener('ended', function() {
        console.log('Music ended event fired');
        if (musicEnabled) {
          console.log('Restarting music for continuous loop');
          bgMusic.currentTime = 0;
          bgMusic.play().catch(e => console.warn('Failed to restart music:', e));
        }
      });

      // Handle loading state
      bgMusic.addEventListener('loadstart', function() {
        console.log('Started loading background music...');
      });

      // Test if the file can be loaded
      console.log('Music source:', bgMusic.src || bgMusic.currentSrc);
      console.log('Music ready state:', bgMusic.readyState);
    }

    function startMusic() {
      console.log('startMusic called. musicEnabled:', musicEnabled, 'bgMusic:', bgMusic);

      if (!musicEnabled || !bgMusic) {
        console.log('Music not enabled or bgMusic not found');
        return;
      }

      // Check if the audio file is loaded
      if (bgMusic.readyState < 2) {
        console.log('Audio not ready, waiting for load...');
        bgMusic.addEventListener('canplay', function() {
          console.log('Audio now ready, attempting to play...');
          attemptPlay();
        }, { once: true });

        // Force load the audio
        bgMusic.load();
        return;
      }

      attemptPlay();

      function attemptPlay() {
        try {
          // Reset to beginning for fresh start
          bgMusic.currentTime = 0;
          console.log('Reset music to beginning');

          // Play the music with looping
          const playPromise = bgMusic.play();
          console.log('Called bgMusic.play(), promise:', playPromise);

          // Handle play promise (required by modern browsers)
          if (playPromise !== undefined) {
            playPromise.then(() => {
              console.log('✅ Background music started successfully and will loop continuously');
            }).catch(error => {
              console.error('❌ Could not start background music:', error);
              console.error('Error name:', error.name);
              console.error('Error message:', error.message);

              // Try alternative approach
              if (error.name === 'NotAllowedError') {
                console.log('🔄 Trying user interaction approach...');
                alert('Click OK to enable music (browser requires user interaction)');
                bgMusic.play().catch(e => console.error('Still failed:', e));
              }
            });
          }
        } catch (error) {
          console.error('❌ Error starting music:', error);
          console.error('Trying to reload audio file...');
          bgMusic.load();
        }
      }
    }

    function stopMusic() {
      console.log('stopMusic called. bgMusic:', bgMusic, 'paused:', bgMusic ? bgMusic.paused : 'N/A');

      if (bgMusic && !bgMusic.paused) {
        bgMusic.pause();
        console.log('✅ Background music stopped');
      } else {
        console.log('Music already paused or bgMusic not found');
      }
    }

    // Initialize inventory
    function initializeInventory() {
      inventory = {};
      for (const mineral in mineralData) {
        inventory[mineral] = 0;
      }
    }

    // Initialize mining display
    function initializeMiningDisplay() {
      const chunksContainer = document.getElementById('mining-chunks');
      chunksContainer.innerHTML = '';
      miningChunks = [];
      chunksMinedCount = 0;
      currentMiningIndex = 0;

      // Get current tier color
      const tierColor = tierColors[Math.min(currentTier, tierColors.length - 1)];

      // Create 150 chunks (15x10 grid)
      for (let i = 0; i < totalChunks; i++) {
        const chunk = document.createElement('div');
        chunk.className = 'chunk';
        chunk.dataset.index = i;

        // Set tier-based color
        chunk.style.background = tierColor;

        // Randomly place some mineral chunks (10% chance)
        if (Math.random() < 0.1) {
          chunk.classList.add('mineral');
        }

        chunksContainer.appendChild(chunk);
        miningChunks.push({
          element: chunk,
          mined: false,
          hasMineral: chunk.classList.contains('mineral')
        });
      }

      updateMiningDisplay();
    }

    // Initialize game
    initializeInventory();
    initializeMusic();
    initializeMiningDisplay();
    updateUI();

    // Auto-save every 30 seconds
    setInterval(() => {
      // Basic save functionality
      console.log('Auto-saving game...');
    }, 30000);

    // Basic UI update function
    function updateUI() {
      document.getElementById("coin-count").textContent = Math.floor(coins);
      document.getElementById("current-depth").textContent = `${Math.floor(currentDepth)}m`;

      // Update inventory count display
      const usedSlots = Object.keys(inventory).filter(key => inventory[key] > 0).length;
      document.getElementById("inventory-count").textContent = `(${usedSlots}/${maxInventorySlots})`;
    }

    // Basic mining function
    function toggleAutoMining() {
      autoMining = !autoMining;
      const btn = document.getElementById("auto-btn");

      if (autoMining) {
        btn.textContent = "⛏️ Stop Mining";
        // Start mining interval
        autoInterval = setInterval(() => {
          currentDepth += 1;
          coins += Math.floor(Math.random() * 10) + 1;
          updateUI();
        }, 2000);
      } else {
        btn.textContent = "⛏️ Start Mining";
        clearInterval(autoInterval);
      }
    }

    // Panel management
    function closeAllPanels() {
      const panels = ['shop-panel', 'settings-panel', 'stats-panel', 'encyclopedia-panel', 'found-minerals-panel', 'events-panel'];
      const overlay = document.getElementById('panel-overlay');

      panels.forEach(panelId => {
        const panel = document.getElementById(panelId);
        if (panel) {
          panel.style.display = 'none';
        }
      });

      overlay.style.display = 'none';
    }

    function openPanel(panelId) {
      closeAllPanels();
      const panel = document.getElementById(panelId);
      const overlay = document.getElementById('panel-overlay');

      if (panel && overlay) {
        panel.style.display = 'block';
        overlay.style.display = 'block';
      }
    }

    // Panel toggle functions
    function toggleShop() { openPanel('shop-panel'); }
    function toggleSettings() { openPanel('settings-panel'); }
    function toggleStats() { openPanel('stats-panel'); }
    function toggleEncyclopedia() { openPanel('encyclopedia-panel'); }
    function toggleFoundMinerals() { openPanel('found-minerals-panel'); }
    function toggleEvents() { openPanel('events-panel'); }

    // Basic shop functions
    function buyDrillEnhancement() {
      const cost = Math.floor(BASE_DRILL_COST * Math.pow(1.3, drillLevel));
      if (coins >= cost) {
        coins -= cost;
        drillLevel++;
        updateUI();
        alert(`Drill Enhancement upgraded to level ${drillLevel}!`);
      }
    }

    function buyVeinChanceBoost() {
      const cost = Math.floor(BASE_VEIN_BOOST_COST * Math.pow(1.1, veinChance / 0.08));
      if (coins >= cost) {
        coins -= cost;
        veinChance += 0.08;
        veinChance = Math.min(veinChance, 0.8);
        updateUI();
        alert("Vein chance increased!");
      }
    }

    function buyInventoryUpgrade() {
      const cost = Math.floor(BASE_INVENTORY_UPGRADE_COST * Math.pow(1.4, inventoryUpgradeLevel));
      if (coins >= cost) {
        coins -= cost;
        inventoryUpgradeLevel++;
        maxInventorySlots = BASE_INVENTORY_SLOTS + (inventoryUpgradeLevel * INVENTORY_UPGRADE_SLOTS);
        updateUI();
        alert(`Inventory expanded to ${maxInventorySlots} slots! (+5 slots)`);
      }
    }

    function sellMinerals() {
      let totalValue = 0;
      for (const mineralName in inventory) {
        if (inventory[mineralName] > 0) {
          totalValue += inventory[mineralName] * (mineralData[mineralName]?.value || 1);
          inventory[mineralName] = 0;
        }
      }

      if (totalValue > 0) {
        coins += totalValue;
        updateUI();
        alert(`Sold minerals for ${totalValue} coins!`);
      }
    }

    // Settings functions
    function toggleMusic() {
      musicEnabled = !musicEnabled;
      const btn = document.getElementById("music-btn");

      if (musicEnabled) {
        startMusic();
        btn.textContent = "🔊 Music On";
      } else {
        stopMusic();
        btn.textContent = "🔈 Music Off";
      }
    }

    function toggleSoundEffects() {
      soundEffectsEnabled = !soundEffectsEnabled;
      const btn = document.getElementById("sfx-btn");
      btn.textContent = soundEffectsEnabled ? "🔉 SFX On" : "🔇 SFX Off";
    }

    function updateScreenSize(scale) {
      const container = document.querySelector('.ui-container');
      const baseWidth = 1200;
      const baseHeight = 800;

      currentScreenScale = parseInt(scale);

      const newWidth = Math.round(baseWidth * (scale / 100));
      const newHeight = Math.round(baseHeight * (scale / 100));

      container.style.width = newWidth + 'px';
      container.style.height = newHeight + 'px';

      document.getElementById('size-display').textContent = `${newWidth}x${newHeight}`;
    }

    function toggleFullscreen() {
      const container = document.querySelector('.ui-container');
      const btn = document.getElementById('fullscreen-btn');

      if (isFullscreen) {
        container.style.position = 'relative';
        container.style.top = '';
        container.style.left = '';
        container.style.transform = '';
        container.style.width = '';
        container.style.height = '';
        container.style.zIndex = '';
        document.body.style.overflow = '';
        document.body.style.padding = '20px';

        updateScreenSize(currentScreenScale);
        btn.textContent = '🖥️ Toggle Fullscreen';
        isFullscreen = false;
      } else {
        container.style.position = 'fixed';
        container.style.top = '50%';
        container.style.left = '50%';
        container.style.transform = 'translate(-50%, -50%)';
        container.style.width = '95vw';
        container.style.height = '95vh';
        container.style.zIndex = '1000';
        document.body.style.overflow = 'hidden';
        document.body.style.padding = '0';

        btn.textContent = '🖥️ Exit Fullscreen';
        isFullscreen = true;
      }
    }

    // Basic save/load functions
    function saveGame() {
      const gameData = {
        coins, currentDepth, currentTier, inventory, veinChance,
        drillLevel, inventoryUpgradeLevel, maxInventorySlots,
        soundEffectsEnabled, musicEnabled, currentScreenScale, isFullscreen
      };
      localStorage.setItem('idleMinerSave', JSON.stringify(gameData));
      alert("Game saved!");
    }

    function loadGame() {
      const saved = localStorage.getItem('idleMinerSave');
      if (saved) {
        try {
          const gameData = JSON.parse(saved);
          coins = gameData.coins || 0;
          currentDepth = gameData.currentDepth || 0;
          currentTier = gameData.currentTier || 0;
          inventory = gameData.inventory || {};
          veinChance = gameData.veinChance || 0;
          drillLevel = gameData.drillLevel || 0;
          inventoryUpgradeLevel = gameData.inventoryUpgradeLevel || 0;
          maxInventorySlots = gameData.maxInventorySlots || BASE_INVENTORY_SLOTS;
          soundEffectsEnabled = gameData.soundEffectsEnabled !== undefined ? gameData.soundEffectsEnabled : true;
          musicEnabled = gameData.musicEnabled !== undefined ? gameData.musicEnabled : false;
          currentScreenScale = gameData.currentScreenScale || 100;
          isFullscreen = gameData.isFullscreen || false;

          updateUI();
          alert("Game loaded successfully!");
        } catch (e) {
          alert("Error loading save file");
        }
      } else {
        alert("No save file found");
      }
    }

    function resetGame() {
      if (!confirm("Are you sure you want to reset the game? This cannot be undone!")) return;

      coins = 0;
      currentDepth = 0;
      currentTier = 0;
      inventory = {};
      veinChance = 0;
      drillLevel = 0;
      inventoryUpgradeLevel = 0;
      maxInventorySlots = BASE_INVENTORY_SLOTS;

      if (autoMining) {
        clearInterval(autoInterval);
        autoMining = false;
        document.getElementById("auto-btn").textContent = "⛏️ Start Mining";
      }

      updateUI();
      alert("Game reset!");
    }

    // Initialize screen size
    updateScreenSize(currentScreenScale);
    document.getElementById('size-slider').value = currentScreenScale;

    // Initialize button states
    document.getElementById("music-btn").textContent = musicEnabled ? "🔊 Music On" : "🔈 Music Off";
    document.getElementById("sfx-btn").textContent = soundEffectsEnabled ? "🔉 SFX On" : "🔇 SFX Off";
  </script>
</body>
</html>
