# Idle Miner - Modular JavaScript Game

A browser-based idle mining game built with ES6 modules for better code organization and maintainability.

## 🎮 Game Features

- **Visual Mining System**: 15x10 grid showing mining progress with tier-based colors
- **Inventory Management**: Limited inventory slots (8 base + upgrades) to force player interaction
- **Progressive Upgrades**: Drill enhancements, vein chance boosts, inventory expansions
- **Random Events**: Mining disruptions that add challenge and variety
- **Audio System**: Background music and sound effects with user file loading
- **Save/Load System**: Persistent game state with auto-save functionality
- **Responsive UI**: Customizable screen sizes and fullscreen mode

## 📁 Project Structure

```
Idle_Miner/
├── index.html              # Main HTML file
├── js/                     # Modular JavaScript files
│   ├── main.js             # Main initialization and game loop
│   ├── gameState.js        # Game state management
│   ├── constants.js        # Game constants and configuration
│   ├── mining.js           # Mining system and visual display
│   ├── inventory.js        # Inventory management
│   ├── shop.js             # Shop and upgrade system
│   ├── audio.js            # Audio system management
│   ├── ui.js               # User interface management
│   ├── events.js           # Random event system
│   ├── saveLoad.js         # Save/load functionality
│   └── utils.js            # Utility functions
└── README.md               # This file
```

## 🏗️ Module Architecture

### **constants.js**
- Game balance constants (mining times, costs, etc.)
- Mineral data and tier definitions
- Event type definitions
- Tier colors and display constants

### **gameState.js**
- Centralized game state management
- State getters/setters with validation
- Import/export functionality for save/load
- Inventory and statistics tracking

### **mining.js**
- Core mining logic and calculations
- Visual mining display management
- Mining timer system
- Chunk-based progression

### **inventory.js**
- Inventory slot management
- Mineral storage and retrieval
- Inventory limitation system
- Selling and value calculations

### **shop.js**
- Upgrade purchase functions
- Cost calculations with scaling
- Auto-sell system
- Prestige mechanics

### **audio.js**
- Background music management
- Sound effect system
- User file loading for custom music
- Audio state management

### **ui.js**
- User interface updates
- Panel management system
- Screen size and fullscreen controls
- Responsive design handling

### **events.js**
- Random event generation
- Event effect management
- Event history tracking
- Mining disruption mechanics

### **saveLoad.js**
- Local storage persistence
- Game state serialization
- Auto-save functionality
- Backup and restore system

### **utils.js**
- Notification system
- Number formatting utilities
- Animation helpers
- Common utility functions

### **main.js**
- Module imports and initialization
- Global function exposure for HTML
- Game loop management
- Error handling and debugging

## 🚀 Getting Started

1. **Clone or download** the project files
2. **Open `index.html`** in a modern web browser that supports ES6 modules
3. **Start playing** - the game will initialize automatically

### Browser Requirements
- Modern browser with ES6 module support
- Chrome 61+, Firefox 60+, Safari 10.1+, Edge 16+

## 🎯 Key Improvements from Modularization

### **Code Organization**
- **Separation of Concerns**: Each module handles a specific aspect of the game
- **Maintainability**: Easier to find and modify specific functionality
- **Reusability**: Modules can be easily reused or replaced

### **Development Benefits**
- **Debugging**: Easier to isolate and fix issues
- **Testing**: Individual modules can be tested independently
- **Collaboration**: Multiple developers can work on different modules

### **Performance**
- **Lazy Loading**: Modules are loaded as needed
- **Tree Shaking**: Unused code can be eliminated by bundlers
- **Caching**: Individual modules can be cached separately

## 🎮 Gameplay Controls

### **Keyboard Shortcuts**
- `Space`: Toggle mining
- `S`: Sell all minerals
- `Ctrl+S`: Save game
- `Ctrl+L`: Load game
- `Escape`: Close panels
- `1-6`: Open panels (Shop, Stats, Encyclopedia, Found Minerals, Events, Settings)
- `M`: Toggle music
- `F`: Toggle fullscreen

### **Game Mechanics**
- **Mining**: Click "Start Mining" or press Space to begin
- **Inventory**: Limited to 8 slots initially, buy expansions for +5 slots each
- **Upgrades**: Purchase drill enhancements and vein chance boosts
- **Events**: Random mining disruptions that add challenge
- **Progression**: Advance through tiers to unlock new minerals

## 🔧 Development

### **Adding New Features**
1. Identify the appropriate module for your feature
2. Add necessary constants to `constants.js`
3. Update game state in `gameState.js` if needed
4. Implement functionality in the relevant module
5. Update UI in `ui.js` if required
6. Export/import functions as needed

### **Module Dependencies**
- Avoid circular dependencies between modules
- Use `gameState.js` as the central data store
- Import only what you need from each module

### **Debugging**
- Use browser console: `window.game` and `window.debugGame` objects
- Check individual module functionality
- Monitor performance with built-in performance monitoring

## 📝 Future Enhancements

- **Bundle System**: Add webpack or rollup for production builds
- **TypeScript**: Convert to TypeScript for better type safety
- **Testing**: Add unit tests for individual modules
- **Web Workers**: Move heavy calculations to background threads
- **PWA**: Add service worker for offline functionality

## 🐛 Troubleshooting

### **Module Loading Issues**
- Ensure you're serving the files over HTTP/HTTPS (not file://)
- Check browser console for import errors
- Verify all file paths are correct

### **Game Not Loading**
- Check browser compatibility (ES6 modules required)
- Clear browser cache and reload
- Check for JavaScript errors in console

### **Save/Load Problems**
- Ensure localStorage is enabled in browser
- Check for quota exceeded errors
- Try clearing browser data and starting fresh

## 📄 License

This project is open source and available under the MIT License.
