import { UPGRADES } from './constants.js';

// Game State Management
class GameState {
  constructor() {
    // Core game state
    this.coins = 0;
    this.miningSpeed = 1;
    this.currentDepth = 0;
    this.depthProgress = 0;
    this.currentTier = 0;
    this.autoMining = false;
    this.autoInterval = null;
    this.inventory = {};
    this.veinChance = 0;
    this.veinChanceBoostCooldown = false;
    this.prestigeBonus = 1;
    this.totalEarnedCoins = 0;
    this.autoSellEnabled = false;
    this.mineralsDiscovered = new Set();
    this.lastSaveTime = Date.now();
    
    // Settings
    this.soundEffectsEnabled = true;
    this.musicEnabled = false;
    this.currentScreenScale = 100;
    this.isFullscreen = false;

    // Mining display variables
    this.miningChunks = [];
    this.chunksMinedCount = 0;
    this.currentMiningIndex = 0; // Track current mining position for left-to-right

    // Upgrade levels
    this.drillLevel = 0;
    this.inventoryUpgradeLevel = 0;

    // Inventory system
    this.maxInventorySlots = UPGRADES.BASE_INVENTORY_SLOTS;

    // Found minerals tracking
    this.foundMinerals = {}; // Track count of each mineral type found

    // Event system
    this.activeEvents = [];
    this.eventHistory = [];
    this.lastEventTime = 0;
    this.eventCooldown = 60000; // 1 minute between events
    this.currentEventPopup = null; // Track current event popup

    // Timer system
    this.miningTimer = null;
    this.nextMineTime = 0;
    this.timerUpdateInterval = null;

    // Notification throttling
    this.lastInventoryWarning = 0;
    this.lastInventoryFullWarning = 0;

    // Achievements
    this.achievements = {
      firstMineral: false,
      firstVein: false,
      firstUpgrade: false,
      firstSale: false,
      firstPrestige: false
    };

    // Game Statistics
    this.gameStats = {
      totalMineralsMined: 0,
      totalVeinsFound: 0,
      timePlayed: 0,
      highestValueMineral: "",
      deepestDepth: 0,
      veinSuccessRate: 0,
      largestVeinFound: 0,
      totalVeinMinerals: 0,
      averageVeinSize: 0
    };
  }

  // Getters
  getCoins() { return this.coins; }
  getCurrentDepth() { return this.currentDepth; }
  getCurrentTier() { return this.currentTier; }
  getInventory() { return this.inventory; }
  getVeinChance() { return this.veinChance; }
  getDrillLevel() { return this.drillLevel; }
  getInventoryUpgradeLevel() { return this.inventoryUpgradeLevel; }
  getMaxInventorySlots() { return this.maxInventorySlots; }
  isAutoMining() { return this.autoMining; }
  isSoundEffectsEnabled() { return this.soundEffectsEnabled; }
  isMusicEnabled() { return this.musicEnabled; }
  getCurrentScreenScale() { return this.currentScreenScale; }
  isFullscreenMode() { return this.isFullscreen; }
  getActiveEvents() { return this.activeEvents; }
  getMiningChunks() { return this.miningChunks; }
  getChunksMinedCount() { return this.chunksMinedCount; }
  getCurrentMiningIndex() { return this.currentMiningIndex; }

  // Setters
  setCoins(value) { this.coins = Math.max(0, value); }
  addCoins(amount) { this.coins += amount; this.totalEarnedCoins += amount; }
  subtractCoins(amount) { this.coins = Math.max(0, this.coins - amount); }
  
  setCurrentDepth(value) { this.currentDepth = Math.max(0, value); }
  addDepth(amount) { this.currentDepth += amount; }
  
  setCurrentTier(value) { this.currentTier = Math.max(0, value); }
  incrementTier() { this.currentTier++; }
  
  setVeinChance(value) { this.veinChance = Math.max(0, Math.min(1, value)); }
  addVeinChance(amount) { this.veinChance = Math.max(0, Math.min(1, this.veinChance + amount)); }
  
  setDrillLevel(value) { this.drillLevel = Math.max(0, value); }
  incrementDrillLevel() { this.drillLevel++; }
  
  setInventoryUpgradeLevel(value) { 
    this.inventoryUpgradeLevel = Math.max(0, value);
    this.updateMaxInventorySlots();
  }
  incrementInventoryUpgradeLevel() { 
    this.inventoryUpgradeLevel++;
    this.updateMaxInventorySlots();
  }
  
  updateMaxInventorySlots() {
    this.maxInventorySlots = UPGRADES.BASE_INVENTORY_SLOTS + 
      (this.inventoryUpgradeLevel * UPGRADES.INVENTORY_UPGRADE_SLOTS);
  }
  
  setAutoMining(value) { this.autoMining = value; }
  setSoundEffectsEnabled(value) { this.soundEffectsEnabled = value; }
  setMusicEnabled(value) { this.musicEnabled = value; }
  setCurrentScreenScale(value) { this.currentScreenScale = value; }
  setFullscreen(value) { this.isFullscreen = value; }
  
  setMiningChunks(chunks) { this.miningChunks = chunks; }
  setChunksMinedCount(count) { this.chunksMinedCount = count; }
  incrementChunksMinedCount() { this.chunksMinedCount++; }
  setCurrentMiningIndex(index) { this.currentMiningIndex = index; }
  incrementCurrentMiningIndex() { this.currentMiningIndex++; }

  // Inventory methods
  addMineralToInventory(mineralName, amount = 1) {
    if (!this.inventory[mineralName]) {
      this.inventory[mineralName] = 0;
    }
    this.inventory[mineralName] += amount;
  }

  removeMineralFromInventory(mineralName, amount = 1) {
    if (this.inventory[mineralName]) {
      this.inventory[mineralName] = Math.max(0, this.inventory[mineralName] - amount);
      if (this.inventory[mineralName] === 0) {
        delete this.inventory[mineralName];
      }
    }
  }

  clearInventory() {
    this.inventory = {};
  }

  getUsedInventorySlots() {
    return Object.keys(this.inventory).filter(key => this.inventory[key] > 0).length;
  }

  hasInventorySpace() {
    return this.getUsedInventorySlots() < this.maxInventorySlots;
  }

  canAddMineralToInventory(mineralName) {
    // If mineral already exists in inventory, we can always add more
    if (this.inventory[mineralName] > 0) {
      return true;
    }
    // If mineral doesn't exist, check if we have space for a new type
    return this.hasInventorySpace();
  }

  // Event methods
  addActiveEvent(event) {
    this.activeEvents.push(event);
  }

  removeActiveEvent(eventId) {
    this.activeEvents = this.activeEvents.filter(event => event.id !== eventId);
  }

  clearActiveEvents() {
    this.activeEvents = [];
  }

  // Statistics methods
  incrementMineralsMined() {
    this.gameStats.totalMineralsMined++;
  }

  incrementVeinsFound() {
    this.gameStats.totalVeinsFound++;
  }

  updateDeepestDepth() {
    if (this.currentDepth > this.gameStats.deepestDepth) {
      this.gameStats.deepestDepth = this.currentDepth;
    }
  }

  // Export state for saving
  exportState() {
    return {
      coins: this.coins,
      miningSpeed: this.miningSpeed,
      currentDepth: this.currentDepth,
      depthProgress: this.depthProgress,
      currentTier: this.currentTier,
      inventory: this.inventory,
      veinChance: this.veinChance,
      prestigeBonus: this.prestigeBonus,
      totalEarnedCoins: this.totalEarnedCoins,
      autoSellEnabled: this.autoSellEnabled,
      mineralsDiscovered: Array.from(this.mineralsDiscovered),
      soundEffectsEnabled: this.soundEffectsEnabled,
      musicEnabled: this.musicEnabled,
      currentScreenScale: this.currentScreenScale,
      isFullscreen: this.isFullscreen,
      drillLevel: this.drillLevel,
      inventoryUpgradeLevel: this.inventoryUpgradeLevel,
      maxInventorySlots: this.maxInventorySlots,
      foundMinerals: this.foundMinerals,
      achievements: this.achievements,
      gameStats: this.gameStats
    };
  }

  // Import state from save
  importState(data) {
    this.coins = data.coins || 0;
    this.miningSpeed = data.miningSpeed || 1;
    this.currentDepth = data.currentDepth || 0;
    this.depthProgress = data.depthProgress || 0;
    this.currentTier = data.currentTier || 0;
    this.inventory = data.inventory || {};
    this.veinChance = data.veinChance || 0;
    this.prestigeBonus = data.prestigeBonus || 1;
    this.totalEarnedCoins = data.totalEarnedCoins || 0;
    this.autoSellEnabled = data.autoSellEnabled || false;
    this.mineralsDiscovered = new Set(data.mineralsDiscovered || []);
    this.soundEffectsEnabled = data.soundEffectsEnabled !== undefined ? data.soundEffectsEnabled : true;
    this.musicEnabled = data.musicEnabled !== undefined ? data.musicEnabled : false;
    this.currentScreenScale = data.currentScreenScale || 100;
    this.isFullscreen = data.isFullscreen || false;
    this.drillLevel = data.drillLevel || 0;
    this.inventoryUpgradeLevel = data.inventoryUpgradeLevel || 0;
    this.maxInventorySlots = data.maxInventorySlots || UPGRADES.BASE_INVENTORY_SLOTS;
    this.foundMinerals = data.foundMinerals || {};
    this.achievements = data.achievements || {
      firstMineral: false,
      firstVein: false,
      firstUpgrade: false,
      firstSale: false,
      firstPrestige: false
    };
    this.gameStats = data.gameStats || {
      totalMineralsMined: 0,
      totalVeinsFound: 0,
      timePlayed: 0,
      highestValueMineral: "",
      deepestDepth: 0,
      veinSuccessRate: 0,
      largestVeinFound: 0,
      totalVeinMinerals: 0,
      averageVeinSize: 0
    };
  }

  // Reset game state
  reset() {
    this.coins = 0;
    this.miningSpeed = 1;
    this.currentDepth = 0;
    this.depthProgress = 0;
    this.currentTier = 0;
    this.autoMining = false;
    this.inventory = {};
    this.veinChance = 0;
    this.prestigeBonus = 1;
    this.totalEarnedCoins = 0;
    this.autoSellEnabled = false;
    this.mineralsDiscovered = new Set();
    this.drillLevel = 0;
    this.inventoryUpgradeLevel = 0;
    this.maxInventorySlots = UPGRADES.BASE_INVENTORY_SLOTS;
    this.foundMinerals = {};
    this.activeEvents = [];
    this.eventHistory = [];
    this.achievements = {
      firstMineral: false,
      firstVein: false,
      firstUpgrade: false,
      firstSale: false,
      firstPrestige: false
    };
    this.gameStats = {
      totalMineralsMined: 0,
      totalVeinsFound: 0,
      timePlayed: 0,
      highestValueMineral: "",
      deepestDepth: 0,
      veinSuccessRate: 0,
      largestVeinFound: 0,
      totalVeinMinerals: 0,
      averageVeinSize: 0
    };
  }
}

// Create and export singleton instance
export const gameState = new GameState();
