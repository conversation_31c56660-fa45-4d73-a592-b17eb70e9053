import { gameState } from './gameState.js';
import { ARTIFACT_DATA } from './artifacts.js';
import { updateUI } from './ui.js';
import { showNotification, showCoinGain } from './utils.js';

// Storage Room Management System

// Initialize storage room
export function initializeStorage() {
  if (!gameState.storage) {
    gameState.storage = {};
  }

  // Initialize all artifacts in storage
  for (const artifactName in ARTIFACT_DATA) {
    if (!gameState.storage[artifactName]) {
      gameState.storage[artifactName] = 0;
    }
  }
}

// Get storage contents
export function getStorageContents() {
  return gameState.storage || {};
}

// Add artifact to storage
export function addArtifactToStorage(artifactName, amount = 1) {
  if (!gameState.storage) {
    initializeStorage();
  }

  if (!gameState.storage[artifactName]) {
    gameState.storage[artifactName] = 0;
  }

  gameState.storage[artifactName] += amount;
  console.log(`Added ${amount} ${artifactName} to storage`);
}

// Remove artifact from storage
export function removeArtifactFromStorage(artifactName, amount = 1) {
  if (!gameState.storage || !gameState.storage[artifactName]) {
    return false;
  }

  if (gameState.storage[artifactName] >= amount) {
    gameState.storage[artifactName] -= amount;
    if (gameState.storage[artifactName] === 0) {
      delete gameState.storage[artifactName];
    }
    return true;
  }

  return false;
}

// Move artifact from inventory to storage
export function moveArtifactToStorage(artifactName, amount = 1) {
  const inventory = gameState.getInventory();

  if (!inventory[artifactName] || inventory[artifactName] < amount) {
    showNotification('Not enough artifacts in inventory!', 'warning');
    return false;
  }

  // Remove from inventory
  gameState.removeMineralFromInventory(artifactName, amount);

  // Add to storage
  addArtifactToStorage(artifactName, amount);

  updateUI();
  showNotification(`Moved ${amount} ${artifactName} to storage!`, 'success');
  return true;
}

// Move artifact from storage to inventory
export function moveArtifactToInventory(artifactName, amount = 1) {
  if (!gameState.storage || !gameState.storage[artifactName] || gameState.storage[artifactName] < amount) {
    showNotification('Not enough artifacts in storage!', 'warning');
    return false;
  }

  // Check if inventory has space
  if (!gameState.canAddMineralToInventory(artifactName)) {
    showNotification('Inventory is full! Sell minerals or buy more inventory space.', 'warning');
    return false;
  }

  // Remove from storage
  removeArtifactFromStorage(artifactName, amount);

  // Add to inventory
  gameState.addMineralToInventory(artifactName, amount);

  updateUI();
  showNotification(`Moved ${amount} ${artifactName} to inventory!`, 'success');
  return true;
}

// Sell artifact from storage
export function sellArtifactFromStorage(artifactName, amount = 1) {
  if (!gameState.storage || !gameState.storage[artifactName] || gameState.storage[artifactName] < amount) {
    showNotification('Not enough artifacts in storage!', 'warning');
    return false;
  }

  const artifact = ARTIFACT_DATA[artifactName];
  if (!artifact) {
    showNotification('Invalid artifact!', 'error');
    return false;
  }

  const totalValue = artifact.value * amount;

  // Remove from storage
  removeArtifactFromStorage(artifactName, amount);

  // Add coins
  gameState.addCoins(totalValue);

  // Show coin gain animation
  showCoinGain(totalValue);

  updateUI();
  showNotification(`Sold ${amount} ${artifactName} for ${totalValue} coins!`, 'success');

  // Play sell sound if enabled
  if (gameState.isSoundEffectsEnabled()) {
    const sellSound = document.getElementById('sell-sound');
    if (sellSound) {
      sellSound.currentTime = 0;
      sellSound.play().catch(e => console.warn('Could not play sell sound:', e));
    }
  }

  return true;
}

// Sell all artifacts from storage
export function sellAllArtifactsFromStorage() {
  if (!gameState.storage) {
    showNotification('Storage is empty!', 'warning');
    return false;
  }

  let totalValue = 0;
  let artifactsSold = 0;
  const soldArtifacts = [];

  for (const artifactName in gameState.storage) {
    if (gameState.storage[artifactName] > 0) {
      const artifact = ARTIFACT_DATA[artifactName];
      if (artifact) {
        const amount = gameState.storage[artifactName];
        const value = artifact.value * amount;
        totalValue += value;
        artifactsSold += amount;
        soldArtifacts.push({ name: artifactName, amount, value });

        // Remove from storage
        delete gameState.storage[artifactName];
      }
    }
  }

  if (totalValue > 0) {
    gameState.addCoins(totalValue);

    // Show coin gain animation
    showCoinGain(totalValue);

    updateUI();
    showNotification(`Sold ${artifactsSold} artifacts for ${totalValue} coins!`, 'success');

    // Play sell sound if enabled
    if (gameState.isSoundEffectsEnabled()) {
      const sellSound = document.getElementById('sell-sound');
      if (sellSound) {
        sellSound.currentTime = 0;
        sellSound.play().catch(e => console.warn('Could not play sell sound:', e));
      }
    }

    return true;
  } else {
    showNotification('No artifacts to sell!', 'warning');
    return false;
  }
}

// Get storage statistics
export function getStorageStats() {
  if (!gameState.storage) {
    return {
      totalArtifacts: 0,
      totalValue: 0,
      uniqueTypes: 0,
      mostValuable: null
    };
  }

  const stats = {
    totalArtifacts: 0,
    totalValue: 0,
    uniqueTypes: 0,
    mostValuable: null
  };

  let highestValue = 0;

  for (const artifactName in gameState.storage) {
    if (gameState.storage[artifactName] > 0) {
      const artifact = ARTIFACT_DATA[artifactName];
      if (artifact) {
        const count = gameState.storage[artifactName];
        const totalValue = count * artifact.value;

        stats.totalArtifacts += count;
        stats.totalValue += totalValue;
        stats.uniqueTypes++;

        if (artifact.value > highestValue) {
          highestValue = artifact.value;
          stats.mostValuable = artifactName;
        }
      }
    }
  }

  return stats;
}

// Get sorted storage list for display
export function getStorageList() {
  if (!gameState.storage) {
    return [];
  }

  const sortedArtifacts = [];

  for (const artifactName in gameState.storage) {
    if (gameState.storage[artifactName] > 0) {
      const artifact = ARTIFACT_DATA[artifactName];
      if (artifact) {
        sortedArtifacts.push({
          name: artifactName,
          value: artifact.value,
          color: artifact.color,
          rarity: artifact.rarity,
          description: artifact.description,
          count: gameState.storage[artifactName]
        });
      }
    }
  }

  // Sort by value (highest first)
  sortedArtifacts.sort((a, b) => b.value - a.value);
  return sortedArtifacts;
}

// Check if storage has any artifacts
export function hasArtifactsInStorage() {
  if (!gameState.storage) return false;

  for (const artifactName in gameState.storage) {
    if (gameState.storage[artifactName] > 0) {
      return true;
    }
  }

  return false;
}

// Get storage capacity info (for future expansion)
export function getStorageCapacity() {
  return {
    used: getStorageStats().totalArtifacts,
    max: -1, // Unlimited for now
    unlimited: true
  };
}

// Update storage display
export function updateStorageDisplay() {
  const storageList = document.getElementById("storage-list");
  if (!storageList) return;

  storageList.innerHTML = "";
  const sortedArtifacts = getStorageList();

  if (sortedArtifacts.length === 0) {
    storageList.innerHTML = '<p style="text-align: center; color: #ccc; margin: 20px;">No artifacts in storage</p>';
    return;
  }

  // Display storage items
  sortedArtifacts.forEach(artifact => {
    const div = document.createElement("div");
    div.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      margin: 5px 0;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 6px;
      border-left: 4px solid ${artifact.color};
    `;

    const totalValue = artifact.count * artifact.value;

    const itemInfo = document.createElement("div");
    itemInfo.style.flex = "1";
    itemInfo.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <span style="font-size: 16px;">🏺</span>
        <strong style="color: ${getRarityColor(artifact.rarity)};">${artifact.name}</strong>
        <span style="color: #ccc;">(${artifact.count})</span>
      </div>
      <div style="font-size: 12px; color: #999; margin-top: 4px;">${artifact.description}</div>
      <div style="font-size: 14px; color: #4CAF50; margin-top: 4px;">Value: ${totalValue} coins</div>
    `;

    const buttonContainer = document.createElement("div");
    buttonContainer.style.cssText = "display: flex; gap: 5px; flex-direction: column;";

    const moveBtn = document.createElement("button");
    moveBtn.textContent = "→Inventory";
    moveBtn.style.cssText = `
      padding: 4px 8px;
      font-size: 10px;
      background: linear-gradient(to bottom, #2196F3, #1976D2);
      border: none;
      border-radius: 4px;
      color: white;
      cursor: pointer;
    `;
    moveBtn.onclick = () => moveArtifactToInventory(artifact.name, 1);

    const sellBtn = document.createElement("button");
    sellBtn.textContent = "Sell";
    sellBtn.style.cssText = `
      padding: 4px 8px;
      font-size: 10px;
      background: linear-gradient(to bottom, #4CAF50, #45a049);
      border: none;
      border-radius: 4px;
      color: white;
      cursor: pointer;
    `;
    sellBtn.onclick = () => sellArtifactFromStorage(artifact.name, 1);

    buttonContainer.appendChild(moveBtn);
    buttonContainer.appendChild(sellBtn);

    div.appendChild(itemInfo);
    div.appendChild(buttonContainer);
    storageList.appendChild(div);
  });
}

// Helper function for rarity colors
function getRarityColor(rarity) {
  switch (rarity) {
    case "Common": return "#FFFFFF";
    case "Uncommon": return "#1EFF00";
    case "Rare": return "#0070DD";
    case "Epic": return "#A335EE";
    case "Legendary": return "#FF8000";
    default: return "#FFFFFF";
  }
}
