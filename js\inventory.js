import { gameState } from './gameState.js';
import { MINERAL_DATA } from './constants.js';
import { ARTIFACT_DATA } from './artifacts.js';
import { updateUI } from './ui.js';
import { showNotification } from './utils.js';

// Inventory Management System

export function initializeInventory() {
  const inventory = {};
  for (const mineral in MINERAL_DATA) {
    inventory[mineral] = 0;
  }
  // Also initialize artifacts in inventory
  for (const artifact in ARTIFACT_DATA) {
    inventory[artifact] = 0;
  }
  gameState.inventory = inventory;
}

export function getUsedInventorySlots() {
  return gameState.getUsedInventorySlots();
}

export function hasInventorySpace() {
  return gameState.hasInventorySpace();
}

export function canAddMineralToInventory(mineralName) {
  return gameState.canAddMineralToInventory(mineralName);
}

export function addMineralToInventory(mineralName, amount = 1) {
  if (!canAddMineralToInventory(mineralName)) {
    return false;
  }

  gameState.addMineralToInventory(mineralName, amount);
  return true;
}

export function removeMineralFromInventory(mineralName, amount = 1) {
  gameState.removeMineralFromInventory(mineralName, amount);
}

export function sellMineral(mineralName, value) {
  const inventory = gameState.getInventory();
  if (inventory[mineralName] && inventory[mineralName] > 0) {
    const totalValue = value;
    gameState.addCoins(totalValue);
    gameState.removeMineralFromInventory(mineralName, 1);

    // Show coin gain animation
    showCoinGain(totalValue);

    updateUI();
    showNotification(`Sold ${mineralName} for ${totalValue} coins!`, 'success');
  }
}

export function sellAllMinerals() {
  const inventory = gameState.getInventory();
  let totalValue = 0;
  let mineralsSold = 0;

  for (const mineralName in inventory) {
    if (inventory[mineralName] > 0) {
      const mineralValue = MINERAL_DATA[mineralName]?.value || 1;
      const amount = inventory[mineralName];
      totalValue += amount * mineralValue;
      mineralsSold += amount;
      gameState.removeMineralFromInventory(mineralName, amount);
    }
  }

  if (totalValue > 0) {
    gameState.addCoins(totalValue);

    // Show coin gain animation
    showCoinGain(totalValue);

    updateUI();
    showNotification(`Sold ${mineralsSold} minerals for ${totalValue} coins!`, 'success');

    // Play sell sound if enabled
    if (gameState.isSoundEffectsEnabled()) {
      const sellSound = document.getElementById('sell-sound');
      if (sellSound) {
        sellSound.currentTime = 0;
        sellSound.play().catch(e => console.warn('Could not play sell sound:', e));
      }
    }
  } else {
    showNotification('No minerals to sell!', 'warning');
  }
}

export function getInventoryValue() {
  const inventory = gameState.getInventory();
  let totalValue = 0;

  for (const mineralName in inventory) {
    if (inventory[mineralName] > 0) {
      const mineralValue = MINERAL_DATA[mineralName]?.value || 1;
      totalValue += inventory[mineralName] * mineralValue;
    }
  }

  return totalValue;
}

export function getInventoryList() {
  const inventory = gameState.getInventory();
  const currentTier = gameState.getCurrentTier();
  const sortedItems = [];

  // Add minerals
  for (let tier = 0; tier <= currentTier; tier++) {
    const tierMinerals = Object.keys(MINERAL_DATA).filter(mineralName => {
      // Check if mineral belongs to this tier (simplified logic)
      const mineralValue = MINERAL_DATA[mineralName].value;
      if (tier === 0) return mineralValue <= 10;
      if (tier === 1) return mineralValue > 10 && mineralValue <= 30;
      if (tier === 2) return mineralValue > 30 && mineralValue <= 150;
      return mineralValue > 150;
    });

    tierMinerals.forEach(mineralName => {
      if (inventory[mineralName] > 0) {
        sortedItems.push({
          name: mineralName,
          value: MINERAL_DATA[mineralName].value,
          color: MINERAL_DATA[mineralName].color,
          tier: tier,
          count: inventory[mineralName],
          type: 'mineral'
        });
      }
    });
  }

  // Add artifacts
  Object.keys(ARTIFACT_DATA).forEach(artifactName => {
    if (inventory[artifactName] > 0) {
      const artifact = ARTIFACT_DATA[artifactName];
      sortedItems.push({
        name: artifactName,
        value: artifact.value,
        color: artifact.color,
        tier: artifact.tier,
        count: inventory[artifactName],
        type: 'artifact',
        rarity: artifact.rarity,
        description: artifact.description
      });
    }
  });

  // Sort by value (highest first)
  sortedItems.sort((a, b) => b.value - a.value);
  return sortedItems;
}

export function updateInventoryDisplay() {
  const inventoryList = document.getElementById("inventory");
  if (!inventoryList) return;

  inventoryList.innerHTML = "";
  const sortedItems = getInventoryList();

  // Display inventory items
  sortedItems.forEach(item => {
    const li = document.createElement("li");
    const totalValue = item.count * item.value;

    const itemContainer = document.createElement("div");
    itemContainer.className = "inventory-item";

    const icon = document.createElement("span");
    icon.className = "mineral-icon";
    icon.style.backgroundColor = item.color;

    if (item.type === 'artifact') {
      icon.title = `${item.name} (${item.rarity} Artifact)`;
      icon.textContent = "🏺"; // Artifact icon
      icon.style.backgroundColor = "transparent";
      icon.style.fontSize = "12px";
    } else {
      icon.title = `${item.name} (Tier ${item.tier})`;
    }

    const nameSpan = document.createElement("span");
    nameSpan.textContent = `${item.name} (${item.count})`;
    nameSpan.className = `${item.type}-${item.name.replace(/\s+/g, '')}`;

    if (item.type === 'artifact') {
      nameSpan.style.fontWeight = "bold";
      nameSpan.style.color = getArtifactRarityColor(item.name);
    }

    const valueSpan = document.createElement("span");
    valueSpan.className = "inventory-value";
    valueSpan.textContent = `$${totalValue}`;

    itemContainer.appendChild(icon);
    itemContainer.appendChild(nameSpan);
    li.appendChild(itemContainer);
    li.appendChild(valueSpan);

    // Add buttons based on item type
    if (item.type === 'artifact') {
      const moveBtn = document.createElement("button");
      moveBtn.textContent = "→Storage";
      moveBtn.style.padding = "2px 6px";
      moveBtn.style.fontSize = "10px";
      moveBtn.style.marginRight = "4px";
      moveBtn.onclick = (e) => {
        e.stopPropagation();
        window.gameActions.moveArtifactToStorage(item.name, 1);
      };
      li.appendChild(moveBtn);
    }

    const sellBtn = document.createElement("button");
    sellBtn.textContent = "Sell";
    sellBtn.style.padding = "2px 8px";
    sellBtn.style.fontSize = "12px";
    sellBtn.onclick = (e) => {
      e.stopPropagation();
      sellMineral(item.name, item.value);
    };

    li.appendChild(sellBtn);
    inventoryList.appendChild(li);
  });

  // Update inventory count display
  const usedSlots = getUsedInventorySlots();
  const maxSlots = gameState.getMaxInventorySlots();
  const inventoryCountElement = document.getElementById("inventory-count");
  if (inventoryCountElement) {
    inventoryCountElement.textContent = `(${usedSlots}/${maxSlots})`;
  }
}

// Helper function to get artifact rarity color
function getArtifactRarityColor(artifactName) {
  const artifact = ARTIFACT_DATA[artifactName];
  if (!artifact) return "#FFFFFF";

  switch (artifact.rarity) {
    case "Common": return "#FFFFFF";
    case "Uncommon": return "#1EFF00";
    case "Rare": return "#0070DD";
    case "Epic": return "#A335EE";
    case "Legendary": return "#FF8000";
    default: return "#FFFFFF";
  }
}

export function showCoinGain(amount) {
  const coinDisplay = document.getElementById('coin-display');
  if (!coinDisplay) return;

  const coinGain = document.createElement('div');
  coinGain.className = 'coin-gain';
  coinGain.textContent = `+${amount}`;

  // Position near the coin display
  const rect = coinDisplay.getBoundingClientRect();
  coinGain.style.left = (rect.left + rect.width / 2) + 'px';
  coinGain.style.top = (rect.top - 10) + 'px';

  document.body.appendChild(coinGain);

  // Remove after animation
  setTimeout(() => {
    if (coinGain.parentNode) {
      coinGain.parentNode.removeChild(coinGain);
    }
  }, 1000);
}

export function checkInventoryWarnings() {
  const usedSlots = getUsedInventorySlots();
  const maxSlots = gameState.getMaxInventorySlots();
  const now = Date.now();

  // Warning when inventory is 80% full
  if (usedSlots >= maxSlots * 0.8 && now - gameState.lastInventoryWarning > 30000) {
    showNotification(`Inventory is ${Math.round((usedSlots / maxSlots) * 100)}% full!`, 'warning');
    gameState.lastInventoryWarning = now;
  }
}

export function getInventoryStats() {
  const inventory = gameState.getInventory();
  const stats = {
    totalMinerals: 0,
    totalValue: 0,
    uniqueTypes: 0,
    mostValuable: null,
    leastValuable: null
  };

  let highestValue = 0;
  let lowestValue = Infinity;

  for (const mineralName in inventory) {
    if (inventory[mineralName] > 0) {
      const count = inventory[mineralName];
      const value = MINERAL_DATA[mineralName]?.value || 1;
      const totalMineralValue = count * value;

      stats.totalMinerals += count;
      stats.totalValue += totalMineralValue;
      stats.uniqueTypes++;

      if (value > highestValue) {
        highestValue = value;
        stats.mostValuable = mineralName;
      }

      if (value < lowestValue) {
        lowestValue = value;
        stats.leastValuable = mineralName;
      }
    }
  }

  return stats;
}

// Coin management functions
export function spendCoins(amount) {
  if (gameState.getCoins() >= amount) {
    gameState.subtractCoins(amount);
    updateUI();
    return true;
  }
  return false;
}

export function hasEnoughCoins(amount) {
  return gameState.getCoins() >= amount;
}
