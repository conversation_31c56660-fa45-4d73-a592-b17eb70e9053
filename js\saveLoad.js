import { gameState } from './gameState.js';
import { updateUI } from './ui.js';
import { showNotification } from './utils.js';
import { initializeMiningDisplay } from './mining.js';
import { initializeAutoSell } from './shop.js';
import { updateAudioButtons } from './audio.js';

// Save/Load System

const SAVE_KEY = 'idleMinerSave';
const SAVE_VERSION = '1.0';

export function saveGame() {
  try {
    const gameData = {
      version: SAVE_VERSION,
      timestamp: Date.now(),
      ...gameState.exportState()
    };
    
    localStorage.setItem(SAVE_KEY, JSON.stringify(gameData));
    showNotification('Game saved successfully!', 'success');
    console.log('Game saved:', gameData);
    return true;
  } catch (error) {
    console.error('Failed to save game:', error);
    showNotification('Failed to save game!', 'error');
    return false;
  }
}

export function loadGame() {
  try {
    const savedData = localStorage.getItem(SAVE_KEY);
    
    if (!savedData) {
      showNotification('No save file found!', 'warning');
      return false;
    }
    
    const gameData = JSON.parse(savedData);
    
    // Version compatibility check
    if (gameData.version !== SAVE_VERSION) {
      console.warn('Save file version mismatch. Attempting to load anyway...');
      showNotification('Save file from different version. Some data may be lost.', 'warning');
    }
    
    // Import the state
    gameState.importState(gameData);
    
    // Reinitialize systems that depend on loaded state
    reinitializeAfterLoad();
    
    showNotification('Game loaded successfully!', 'success');
    console.log('Game loaded:', gameData);
    return true;
  } catch (error) {
    console.error('Failed to load game:', error);
    showNotification('Failed to load game! Save file may be corrupted.', 'error');
    return false;
  }
}

export function resetGame() {
  if (!confirm('Are you sure you want to reset the game? This will delete all progress and cannot be undone!')) {
    return false;
  }
  
  try {
    // Stop any running intervals
    if (gameState.autoInterval) {
      clearInterval(gameState.autoInterval);
    }
    if (gameState.timerUpdateInterval) {
      clearInterval(gameState.timerUpdateInterval);
    }
    
    // Reset game state
    gameState.reset();
    
    // Reinitialize systems
    reinitializeAfterReset();
    
    showNotification('Game reset successfully!', 'success');
    console.log('Game reset');
    return true;
  } catch (error) {
    console.error('Failed to reset game:', error);
    showNotification('Failed to reset game!', 'error');
    return false;
  }
}

export function exportSave() {
  try {
    const gameData = {
      version: SAVE_VERSION,
      timestamp: Date.now(),
      ...gameState.exportState()
    };
    
    const saveString = JSON.stringify(gameData);
    const blob = new Blob([saveString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `idle-miner-save-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showNotification('Save file exported!', 'success');
    return true;
  } catch (error) {
    console.error('Failed to export save:', error);
    showNotification('Failed to export save file!', 'error');
    return false;
  }
}

export function importSave(file) {
  if (!file) {
    showNotification('No file selected!', 'warning');
    return false;
  }
  
  const reader = new FileReader();
  
  reader.onload = function(e) {
    try {
      const gameData = JSON.parse(e.target.result);
      
      // Validate the save file
      if (!validateSaveFile(gameData)) {
        showNotification('Invalid save file format!', 'error');
        return false;
      }
      
      // Confirm import
      if (!confirm('This will overwrite your current progress. Continue?')) {
        return false;
      }
      
      // Import the state
      gameState.importState(gameData);
      
      // Reinitialize systems
      reinitializeAfterLoad();
      
      showNotification('Save file imported successfully!', 'success');
      console.log('Save imported:', gameData);
      return true;
    } catch (error) {
      console.error('Failed to import save:', error);
      showNotification('Failed to import save file! File may be corrupted.', 'error');
      return false;
    }
  };
  
  reader.onerror = function() {
    showNotification('Failed to read save file!', 'error');
  };
  
  reader.readAsText(file);
}

function validateSaveFile(data) {
  // Basic validation to ensure it's a valid save file
  const requiredFields = ['coins', 'currentDepth', 'currentTier', 'inventory'];
  
  for (const field of requiredFields) {
    if (!(field in data)) {
      console.error(`Missing required field: ${field}`);
      return false;
    }
  }
  
  // Validate data types
  if (typeof data.coins !== 'number' || data.coins < 0) {
    console.error('Invalid coins value');
    return false;
  }
  
  if (typeof data.currentDepth !== 'number' || data.currentDepth < 0) {
    console.error('Invalid currentDepth value');
    return false;
  }
  
  if (typeof data.currentTier !== 'number' || data.currentTier < 0) {
    console.error('Invalid currentTier value');
    return false;
  }
  
  if (typeof data.inventory !== 'object' || data.inventory === null) {
    console.error('Invalid inventory value');
    return false;
  }
  
  return true;
}

function reinitializeAfterLoad() {
  // Update UI to reflect loaded state
  updateUI();
  
  // Reinitialize mining display
  initializeMiningDisplay();
  
  // Restart auto-sell if it was enabled
  initializeAutoSell();
  
  // Update audio button states
  updateAudioButtons();
  
  // Update screen size
  const sizeSlider = document.getElementById('size-slider');
  if (sizeSlider) {
    sizeSlider.value = gameState.getCurrentScreenScale();
  }
  
  // Restart auto-mining if it was active
  if (gameState.isAutoMining()) {
    const autoBtn = document.getElementById('auto-btn');
    if (autoBtn) {
      autoBtn.textContent = '⛏️ Stop Mining';
    }
    // Note: Mining interval will be restarted by the mining system
  }
}

function reinitializeAfterReset() {
  // Update UI to reflect reset state
  updateUI();
  
  // Reinitialize mining display
  initializeMiningDisplay();
  
  // Update audio button states
  updateAudioButtons();
  
  // Reset screen size
  const sizeSlider = document.getElementById('size-slider');
  if (sizeSlider) {
    sizeSlider.value = 100;
  }
  
  // Reset mining button
  const autoBtn = document.getElementById('auto-btn');
  if (autoBtn) {
    autoBtn.textContent = '⛏️ Start Mining';
  }
}

// Auto-save functionality
let autoSaveInterval = null;

export function startAutoSave(intervalMinutes = 1) {
  stopAutoSave(); // Clear any existing interval
  
  autoSaveInterval = setInterval(() => {
    saveGame();
    console.log('Auto-save completed');
  }, intervalMinutes * 60 * 1000);
  
  console.log(`Auto-save started (every ${intervalMinutes} minute(s))`);
}

export function stopAutoSave() {
  if (autoSaveInterval) {
    clearInterval(autoSaveInterval);
    autoSaveInterval = null;
    console.log('Auto-save stopped');
  }
}

// Save on page unload
export function initializeSaveOnUnload() {
  window.addEventListener('beforeunload', () => {
    saveGame();
  });
  
  // Also save when the page becomes hidden (mobile/tab switching)
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      saveGame();
    }
  });
}

// Backup system
export function createBackup() {
  try {
    const backupKey = `${SAVE_KEY}_backup_${Date.now()}`;
    const gameData = {
      version: SAVE_VERSION,
      timestamp: Date.now(),
      ...gameState.exportState()
    };
    
    localStorage.setItem(backupKey, JSON.stringify(gameData));
    
    // Keep only the 5 most recent backups
    cleanupOldBackups();
    
    console.log('Backup created:', backupKey);
    return true;
  } catch (error) {
    console.error('Failed to create backup:', error);
    return false;
  }
}

function cleanupOldBackups() {
  try {
    const backupKeys = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(`${SAVE_KEY}_backup_`)) {
        backupKeys.push(key);
      }
    }
    
    // Sort by timestamp (newest first)
    backupKeys.sort((a, b) => {
      const timestampA = parseInt(a.split('_').pop());
      const timestampB = parseInt(b.split('_').pop());
      return timestampB - timestampA;
    });
    
    // Remove old backups (keep only 5 most recent)
    for (let i = 5; i < backupKeys.length; i++) {
      localStorage.removeItem(backupKeys[i]);
      console.log('Removed old backup:', backupKeys[i]);
    }
  } catch (error) {
    console.error('Failed to cleanup old backups:', error);
  }
}

export function listBackups() {
  const backups = [];
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith(`${SAVE_KEY}_backup_`)) {
      const timestamp = parseInt(key.split('_').pop());
      backups.push({
        key,
        timestamp,
        date: new Date(timestamp).toLocaleString()
      });
    }
  }
  
  // Sort by timestamp (newest first)
  backups.sort((a, b) => b.timestamp - a.timestamp);
  
  return backups;
}

export function restoreFromBackup(backupKey) {
  try {
    const backupData = localStorage.getItem(backupKey);
    
    if (!backupData) {
      showNotification('Backup not found!', 'error');
      return false;
    }
    
    const gameData = JSON.parse(backupData);
    
    if (!confirm('This will overwrite your current progress with the backup. Continue?')) {
      return false;
    }
    
    // Import the backup state
    gameState.importState(gameData);
    
    // Reinitialize systems
    reinitializeAfterLoad();
    
    showNotification('Backup restored successfully!', 'success');
    console.log('Backup restored:', backupKey);
    return true;
  } catch (error) {
    console.error('Failed to restore backup:', error);
    showNotification('Failed to restore backup!', 'error');
    return false;
  }
}
