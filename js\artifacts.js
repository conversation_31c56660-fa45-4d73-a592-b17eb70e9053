// Artifact System

// Artifact definitions with names, sell prices, and descriptions
export const ARTIFACT_DATA = {
  "Ancient Pickaxe": {
    value: 500,
    color: "#8B4513",
    description: "A weathered mining tool from a bygone era. Its worn handle tells stories of countless excavations.",
    rarity: "Common",
    tier: 0
  },
  "Crystal Compass": {
    value: 1200,
    color: "#87CEEB",
    description: "A mystical navigation device that always points toward the richest mineral veins. Its crystal face glows with inner light.",
    rarity: "Uncommon",
    tier: 1
  },
  "Dwarven Lantern": {
    value: 2500,
    color: "#FFD700",
    description: "An ornate lantern crafted by master dwarven smiths. Its eternal flame never dims, lighting the darkest depths.",
    rarity: "Rare",
    tier: 2
  },
  "Void Stone Tablet": {
    value: 5000,
    color: "#4B0082",
    description: "A mysterious tablet inscribed with ancient mining techniques. The void stone material seems to absorb light itself.",
    rarity: "Epic",
    tier: 3
  },
  "Starfall Meteorite": {
    value: 10000,
    color: "#FF69B4",
    description: "A fragment of a fallen star, pulsing with cosmic energy. <PERSON> says it grants the power to see through solid rock.",
    rarity: "Legendary",
    tier: 4
  }
};

// Artifact tiers based on depth (similar to minerals)
export const ARTIFACT_TIERS = [
  ["Ancient Pickaxe"], // Tier 0 - Surface (0-100m)
  ["Ancient Pickaxe", "Crystal Compass"], // Tier 1 - Shallow (100-300m)
  ["Crystal Compass", "Dwarven Lantern"], // Tier 2 - Medium (300-600m)
  ["Dwarven Lantern", "Void Stone Tablet"], // Tier 3 - Deep (600-1000m)
  ["Void Stone Tablet", "Starfall Meteorite"], // Tier 4+ - Very Deep (1000m+)
];

// Artifact spawn rates (much rarer than minerals)
export const ARTIFACT_SPAWN_RATES = {
  base: 0.005, // 0.5% base chance
  veinMultiplier: 2, // 2x chance during vein discovery
  tierBonus: 0.001 // +0.1% per tier
};

// Get available artifacts for current tier
export function getAvailableArtifacts(tier) {
  const maxTier = Math.min(tier, ARTIFACT_TIERS.length - 1);
  return ARTIFACT_TIERS[maxTier] || [];
}

// Calculate artifact spawn chance
export function calculateArtifactChance(tier, isVein = false) {
  let chance = ARTIFACT_SPAWN_RATES.base;
  
  // Add tier bonus
  chance += tier * ARTIFACT_SPAWN_RATES.tierBonus;
  
  // Multiply for vein discovery
  if (isVein) {
    chance *= ARTIFACT_SPAWN_RATES.veinMultiplier;
  }
  
  // Cap at 5% maximum
  return Math.min(chance, 0.05);
}

// Get random artifact from current tier
export function getRandomArtifact(tier) {
  const availableArtifacts = getAvailableArtifacts(tier);
  if (availableArtifacts.length === 0) return null;
  
  return availableArtifacts[Math.floor(Math.random() * availableArtifacts.length)];
}

// Get artifact rarity color
export function getArtifactRarityColor(artifactName) {
  const artifact = ARTIFACT_DATA[artifactName];
  if (!artifact) return "#FFFFFF";
  
  switch (artifact.rarity) {
    case "Common": return "#FFFFFF";
    case "Uncommon": return "#1EFF00";
    case "Rare": return "#0070DD";
    case "Epic": return "#A335EE";
    case "Legendary": return "#FF8000";
    default: return "#FFFFFF";
  }
}

// Get all artifacts for encyclopedia
export function getAllArtifacts() {
  return Object.keys(ARTIFACT_DATA).map(name => ({
    name,
    ...ARTIFACT_DATA[name]
  }));
}

// Validate artifact name
export function isValidArtifact(artifactName) {
  return artifactName in ARTIFACT_DATA;
}

// Get artifact stats
export function getArtifactStats(artifacts) {
  const stats = {
    totalArtifacts: 0,
    totalValue: 0,
    uniqueTypes: 0,
    mostValuable: null,
    rarityCount: {
      Common: 0,
      Uncommon: 0,
      Rare: 0,
      Epic: 0,
      Legendary: 0
    }
  };

  let highestValue = 0;

  for (const artifactName in artifacts) {
    if (artifacts[artifactName] > 0) {
      const count = artifacts[artifactName];
      const artifact = ARTIFACT_DATA[artifactName];
      
      if (artifact) {
        const totalValue = count * artifact.value;
        
        stats.totalArtifacts += count;
        stats.totalValue += totalValue;
        stats.uniqueTypes++;
        stats.rarityCount[artifact.rarity] += count;
        
        if (artifact.value > highestValue) {
          highestValue = artifact.value;
          stats.mostValuable = artifactName;
        }
      }
    }
  }

  return stats;
}
