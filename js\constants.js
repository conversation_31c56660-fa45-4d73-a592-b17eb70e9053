// Game Balance Constants
export const GAME_BALANCE = {
  BASE_MINING_TIME: 30000, // 30 seconds base mining time
  TIER_TIME_INCREASE: 15000, // +15 seconds per tier
  BASE_UPGRADE_COST: 25,
  BASE_VEIN_BOOST_COST: 60,
  MINING_SPEED_INCREASE: 0.15, // Smaller increments
  PRESTIGE_REQUIREMENT: 5000,
  AUTO_SELL_COST: 2000,
  DEPTH_PER_MINE: 1,
  DEPTH_TIERS: [100, 300, 600, 1000, 1500, 2100, 2800, 3600, 4500, 5500]
};

// Upgrade costs and effects
export const UPGRADES = {
  BASE_DRILL_COST: 25,
  BASE_AUTO_SELL_MINERALS_COST: 500,
  DRILL_SPEED_BOOST: 0.15, // 15% speed increase per level
  BASE_INVENTORY_SLOTS: 8,
  INVENTORY_UPGRADE_SLOTS: 5, // +5 slots per upgrade
  BASE_INVENTORY_UPGRADE_COST: 100
};

// Tier colors for chunks (excluding gold mineral chunks)
export const TIER_COLORS = [
  'rgba(139, 69, 19, 0.8)',    // Tier 0: Brown
  'rgba(105, 105, 105, 0.8)',  // Tier 1: Gray
  'rgba(70, 130, 180, 0.8)',   // Tier 2: Steel Blue
  'rgba(128, 0, 128, 0.8)',    // Tier 3: Purple
  'rgba(220, 20, 60, 0.8)',    // Tier 4: Crimson
  'rgba(255, 140, 0, 0.8)',    // Tier 5: Dark Orange
  'rgba(0, 100, 0, 0.8)',      // Tier 6: Dark Green
  'rgba(25, 25, 112, 0.8)',    // Tier 7: Midnight Blue
  'rgba(139, 0, 139, 0.8)',    // Tier 8: Dark Magenta
  'rgba(0, 0, 0, 0.9)'         // Tier 9: Black
];

// Mineral data with values and colors
export const MINERAL_DATA = {
  "Feldspar": { value: 2, color: "#bdb76b" },
  "Gypsum": { value: 2, color: "#dcdcdc" },
  "Quartz": { value: 3, color: "#f0e68c" },
  "Mica": { value: 4, color: "#eee8aa" },
  "Calcite": { value: 5, color: "#ffebcd" },
  "Fluorite": { value: 6, color: "#98fb98" },
  "Magnetite": { value: 7, color: "#778899" },
  "Hematite": { value: 8, color: "#b22222" },
  "Dolomite": { value: 10, color: "#d3d3d3" },
  "Apatite": { value: 12, color: "#7fffd4" },
  "Barite": { value: 13, color: "#a9a9a9" },
  "Talc": { value: 14, color: "#f5f5f5" },
  "Bauxite": { value: 15, color: "#cd853f" },
  "Sphalerite": { value: 18, color: "#d2b48c" },
  "Chalcopyrite": { value: 20, color: "#b8860b" },
  "Galena": { value: 25, color: "#a9a9a9" },
  "Silver": { value: 40, color: "#c0c0c0" },
  "Topaz": { value: 45, color: "#ffd700" },
  "Gold": { value: 50, color: "#ffd700" },
  "Sapphire": { value: 60, color: "#4682b4" },
  "Emerald": { value: 70, color: "#50c878" },
  "Ruby": { value: 80, color: "#e0115f" },
  "Diamond": { value: 100, color: "#b9f2ff" },
  "Platinum": { value: 120, color: "#e5e4e2" },
  "Darkstone": { value: 150, color: "#343434" },
  "Emberglass": { value: 180, color: "#ff4500" },
  "Froststeel": { value: 220, color: "#add8e6" },
  "Starcrystal": { value: 270, color: "#87ceeb" },
  "Sunshard": { value: 320, color: "#ffd700" },
  "Voidgem": { value: 400, color: "#4b0082" }
};

// Mineral tiers based on depth
export const MINERAL_TIERS = [
  [ // Tier 0 - Surface (0-100m)
    "Feldspar", "Gypsum", "Quartz", "Mica",
    "Calcite", "Fluorite", "Magnetite", "Hematite"
  ],
  [ // Tier 1 - Shallow (100-300m)
    "Dolomite", "Apatite", "Barite", "Talc",
    "Bauxite", "Sphalerite", "Chalcopyrite", "Galena"
  ],
  [ // Tier 2 - Medium (300-600m)
    "Silver", "Topaz", "Gold", "Sapphire",
    "Emerald", "Ruby", "Diamond", "Platinum"
  ],
  [ // Tier 3 - Deep (600-1000m)
    "Darkstone", "Emberglass", "Froststeel",
    "Starcrystal", "Sunshard", "Voidgem"
  ],
  // Additional tiers for deeper levels (same minerals but higher chance of veins)
  [ // Tier 4 (1000-1500m)
    "Silver", "Topaz", "Gold", "Sapphire",
    "Emerald", "Ruby", "Diamond", "Platinum",
    "Darkstone", "Emberglass", "Froststeel",
    "Starcrystal", "Sunshard", "Voidgem"
  ],
  [ // Tier 5 (1500-2100m)
    "Gold", "Sapphire", "Emerald", "Ruby",
    "Diamond", "Platinum", "Darkstone", "Emberglass",
    "Froststeel", "Starcrystal", "Sunshard", "Voidgem"
  ],
  [ // Tier 6 (2100-2800m)
    "Diamond", "Platinum", "Darkstone", "Emberglass",
    "Froststeel", "Starcrystal", "Sunshard", "Voidgem"
  ],
  [ // Tier 7 (2800-3600m)
    "Darkstone", "Emberglass", "Froststeel",
    "Starcrystal", "Sunshard", "Voidgem"
  ],
  [ // Tier 8 (3600-4500m)
    "Emberglass", "Froststeel", "Starcrystal",
    "Sunshard", "Voidgem"
  ],
  [ // Tier 9 (4500-5500m)
    "Starcrystal", "Sunshard", "Voidgem"
  ],
  [ // Tier 10 (5500m+)
    "Voidgem"
  ]
];

// Event definitions
export const EVENT_TYPES = [
  {
    id: 'broken_drill',
    name: 'Broken Drill Bit',
    description: 'Your drill bit has broken! Mining is halted while repairs are made.',
    icon: '🔧',
    duration: 45000, // 45 seconds
    effect: 'mining_halt'
  },
  {
    id: 'cave_in',
    name: 'Mine Cave-In',
    description: 'A section of the mine has collapsed! Clearing debris takes time.',
    icon: '⛰️',
    duration: 60000, // 60 seconds
    effect: 'mining_halt'
  },
  {
    id: 'equipment_malfunction',
    name: 'Equipment Malfunction',
    description: 'Mining equipment is malfunctioning, reducing efficiency.',
    icon: '⚙️',
    duration: 90000, // 90 seconds
    effect: 'slow_mining',
    multiplier: 0.5 // 50% slower
  },
  {
    id: 'power_outage',
    name: 'Power Outage',
    description: 'Electrical systems are down, severely impacting operations.',
    icon: '⚡',
    duration: 75000, // 75 seconds
    effect: 'slow_mining',
    multiplier: 0.3 // 70% slower
  },
  {
    id: 'gas_leak',
    name: 'Gas Leak',
    description: 'Dangerous gases detected! Mining suspended for safety.',
    icon: '☠️',
    duration: 50000, // 50 seconds
    effect: 'mining_halt'
  }
];

// Mining display constants
export const MINING_DISPLAY = {
  TOTAL_CHUNKS: 150, // 15x10 grid
  GRID_WIDTH: 15,
  GRID_HEIGHT: 10,
  MINERAL_SPAWN_RATE: 0.1 // 10% chance
};
