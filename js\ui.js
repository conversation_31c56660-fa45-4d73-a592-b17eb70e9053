import { gameState } from './gameState.js';
import { GAME_BALANCE, MINERAL_DATA } from './constants.js';
import { updateInventoryDisplay } from './inventory.js';
import { updateShopDisplay } from './shop.js';
import { updateStorageDisplay } from './storage.js';

// User Interface Management

export function updateUI() {
  updateCoinDisplay();
  updateDepthDisplay();
  updateInventoryDisplay();
  updateShopDisplay();
  updateMiningButton();
}

function updateCoinDisplay() {
  const coinCountElement = document.getElementById("coin-count");
  if (coinCountElement) {
    coinCountElement.textContent = Math.floor(gameState.getCoins());
  }
}

function updateDepthDisplay() {
  const currentDepthElement = document.getElementById("current-depth");
  if (currentDepthElement) {
    currentDepthElement.textContent = `${Math.floor(gameState.getCurrentDepth())}m`;
  }

  const nextTierElement = document.getElementById("next-tier");
  if (nextTierElement) {
    const currentTier = gameState.getCurrentTier();
    if (currentTier < GAME_BALANCE.DEPTH_TIERS.length) {
      nextTierElement.textContent = `${GAME_BALANCE.DEPTH_TIERS[currentTier]}m`;
    } else {
      nextTierElement.textContent = `Max Depth`;
    }
  }
}

function updateMiningButton() {
  const autoBtn = document.getElementById("auto-btn");
  if (autoBtn) {
    autoBtn.textContent = gameState.isAutoMining() ? "⛏️ Stop Mining" : "⛏️ Start Mining";
  }
}

// Panel Management
export function closeAllPanels() {
  const panels = ['shop-panel', 'settings-panel', 'stats-panel', 'encyclopedia-panel', 'found-minerals-panel', 'events-panel', 'storage-panel'];
  const overlay = document.getElementById('panel-overlay');

  panels.forEach(panelId => {
    const panel = document.getElementById(panelId);
    if (panel) {
      panel.style.display = 'none';
      panel.setAttribute('aria-hidden', 'true');
    }
  });

  if (overlay) {
    overlay.style.display = 'none';
  }

  // Update button states
  updatePanelButtonStates();
}

export function openPanel(panelId) {
  closeAllPanels();
  const panel = document.getElementById(panelId);
  const overlay = document.getElementById('panel-overlay');

  if (panel && overlay) {
    panel.style.display = 'block';
    panel.setAttribute('aria-hidden', 'false');
    overlay.style.display = 'block';

    // Update specific panel content
    updatePanelContent(panelId);
  }

  // Update button states
  updatePanelButtonStates(panelId);
}

function updatePanelContent(panelId) {
  switch (panelId) {
    case 'stats-panel':
      updateStatsPanel();
      break;
    case 'encyclopedia-panel':
      updateEncyclopediaPanel();
      break;
    case 'found-minerals-panel':
      updateFoundMineralsPanel();
      break;
    case 'events-panel':
      updateEventsPanel();
      break;
    case 'storage-panel':
      updateStorageDisplay();
      break;
  }
}

function updatePanelButtonStates(activePanelId = null) {
  const buttons = [
    { id: 'shop-btn', panel: 'shop-panel' },
    { id: 'stats-btn', panel: 'stats-panel' },
    { id: 'encyclopedia-btn', panel: 'encyclopedia-panel' },
    { id: 'found-minerals-btn', panel: 'found-minerals-panel' },
    { id: 'storage-btn', panel: 'storage-panel' },
    { id: 'events-btn', panel: 'events-panel' },
    { id: 'settings-btn', panel: 'settings-panel' }
  ];

  buttons.forEach(({ id, panel }) => {
    const button = document.getElementById(id);
    if (button) {
      const isActive = panel === activePanelId;
      button.setAttribute('aria-pressed', isActive.toString());
    }
  });
}

// Panel toggle functions
export function toggleShop() {
  const isOpen = document.getElementById('shop-panel').style.display === 'block';
  if (isOpen) closeAllPanels();
  else openPanel('shop-panel');
}

export function toggleSettings() {
  const isOpen = document.getElementById('settings-panel').style.display === 'block';
  if (isOpen) closeAllPanels();
  else openPanel('settings-panel');
}

export function toggleStats() {
  const isOpen = document.getElementById('stats-panel').style.display === 'block';
  if (isOpen) closeAllPanels();
  else openPanel('stats-panel');
}

export function toggleEncyclopedia() {
  const isOpen = document.getElementById('encyclopedia-panel').style.display === 'block';
  if (isOpen) closeAllPanels();
  else openPanel('encyclopedia-panel');
}

export function toggleFoundMinerals() {
  const isOpen = document.getElementById('found-minerals-panel').style.display === 'block';
  if (isOpen) closeAllPanels();
  else openPanel('found-minerals-panel');
}

export function toggleEvents() {
  const isOpen = document.getElementById('events-panel').style.display === 'block';
  if (isOpen) closeAllPanels();
  else openPanel('events-panel');
}

export function toggleStorage() {
  const isOpen = document.getElementById('storage-panel').style.display === 'block';
  if (isOpen) closeAllPanels();
  else openPanel('storage-panel');
}

// Screen size management
export function updateScreenSize(scale) {
  const container = document.querySelector('.ui-container');
  const baseWidth = 1200;
  const baseHeight = 800;

  const currentScale = parseInt(scale);
  gameState.setCurrentScreenScale(currentScale);

  const newWidth = Math.round(baseWidth * (scale / 100));
  const newHeight = Math.round(baseHeight * (scale / 100));

  if (container) {
    container.style.width = newWidth + 'px';
    container.style.height = newHeight + 'px';
  }

  const sizeDisplay = document.getElementById('size-display');
  if (sizeDisplay) {
    sizeDisplay.textContent = `${newWidth}x${newHeight}`;
  }
}

export function toggleFullscreen() {
  const container = document.querySelector('.ui-container');
  const btn = document.getElementById('fullscreen-btn');
  const isFullscreen = gameState.isFullscreenMode();

  if (!container || !btn) return;

  if (isFullscreen) {
    // Exit fullscreen
    container.style.position = 'relative';
    container.style.top = '';
    container.style.left = '';
    container.style.transform = '';
    container.style.width = '';
    container.style.height = '';
    container.style.zIndex = '';
    document.body.style.overflow = '';
    document.body.style.padding = '20px';

    updateScreenSize(gameState.getCurrentScreenScale());
    btn.textContent = '🖥️ Toggle Fullscreen';
    gameState.setFullscreen(false);
  } else {
    // Enter fullscreen
    container.style.position = 'fixed';
    container.style.top = '50%';
    container.style.left = '50%';
    container.style.transform = 'translate(-50%, -50%)';
    container.style.width = '95vw';
    container.style.height = '95vh';
    container.style.zIndex = '1000';
    document.body.style.overflow = 'hidden';
    document.body.style.padding = '0';

    btn.textContent = '🖥️ Exit Fullscreen';
    gameState.setFullscreen(true);
  }
}

// Panel content updates
function updateStatsPanel() {
  const statsContent = document.getElementById('stats-content');
  if (!statsContent) return;

  const stats = gameState.gameStats;
  const currentDepth = gameState.getCurrentDepth();
  const totalCoins = gameState.getCoins();
  const totalMinerals = Object.values(gameState.getInventory()).reduce((sum, count) => sum + count, 0);

  statsContent.innerHTML = `
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
      <div>
        <h3>Mining Statistics</h3>
        <p><strong>Current Depth:</strong> ${Math.floor(currentDepth)}m</p>
        <p><strong>Deepest Reached:</strong> ${Math.floor(stats.deepestDepth)}m</p>
        <p><strong>Current Tier:</strong> ${gameState.getCurrentTier()}</p>
        <p><strong>Total Minerals Mined:</strong> ${stats.totalMineralsMined}</p>
        <p><strong>Veins Found:</strong> ${stats.totalVeinsFound}</p>
        <p><strong>Vein Success Rate:</strong> ${stats.totalMineralsMined > 0 ? Math.round((stats.totalVeinsFound / stats.totalMineralsMined) * 100) : 0}%</p>
      </div>
      <div>
        <h3>Economic Statistics</h3>
        <p><strong>Current Coins:</strong> ${Math.floor(totalCoins)}</p>
        <p><strong>Total Earned:</strong> ${Math.floor(gameState.totalEarnedCoins)}</p>
        <p><strong>Inventory Value:</strong> ${getInventoryValue()}</p>
        <p><strong>Current Inventory:</strong> ${totalMinerals} minerals</p>
        <p><strong>Prestige Bonus:</strong> +${Math.round((gameState.prestigeBonus - 1) * 100)}%</p>
        <p><strong>Drill Level:</strong> ${gameState.getDrillLevel()}</p>
      </div>
    </div>
  `;
}

function updateEncyclopediaPanel() {
  // Encyclopedia content is mostly static, defined in HTML
  // Could add dynamic content here if needed
}

function updateFoundMineralsPanel() {
  const foundMineralsContent = document.getElementById('found-minerals-content');
  if (!foundMineralsContent) return;

  const foundMinerals = gameState.foundMinerals;
  const discoveredCount = Object.keys(foundMinerals).length;
  const totalMinerals = Object.keys(MINERAL_DATA).length;

  let content = `
    <p>Discovered: ${discoveredCount}/${totalMinerals} mineral types</p>
    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; margin-top: 20px;">
  `;

  for (const [mineralName, count] of Object.entries(foundMinerals)) {
    const mineralInfo = MINERAL_DATA[mineralName];
    if (mineralInfo) {
      content += `
        <div style="padding: 10px; background: rgba(255,255,255,0.05); border-radius: 5px; border-left: 4px solid ${mineralInfo.color};">
          <strong>${mineralName}</strong><br>
          <span style="color: ${mineralInfo.color};">●</span> Value: ${mineralInfo.value} coins<br>
          Found: ${count} times
        </div>
      `;
    }
  }

  content += '</div>';
  foundMineralsContent.innerHTML = content;
}

function updateEventsPanel() {
  const eventsContent = document.getElementById('events-content');
  if (!eventsContent) return;

  const activeEvents = gameState.getActiveEvents();

  let content = '<h3>Active Events:</h3>';

  if (activeEvents.length === 0) {
    content += '<p>No active events</p>';
  } else {
    content += '<div style="margin-bottom: 20px;">';
    activeEvents.forEach(event => {
      const timeRemaining = Math.max(0, event.endTime - Date.now());
      const secondsRemaining = Math.ceil(timeRemaining / 1000);

      content += `
        <div style="padding: 10px; background: rgba(255,100,100,0.1); border-radius: 5px; margin: 5px 0; border-left: 4px solid #ff6464;">
          <strong>${event.icon} ${event.name}</strong><br>
          <small>${event.description}</small><br>
          <em>Time remaining: ${secondsRemaining}s</em>
        </div>
      `;
    });
    content += '</div>';
  }

  eventsContent.innerHTML = content;
}

function getInventoryValue() {
  const inventory = gameState.getInventory();
  let totalValue = 0;

  for (const mineralName in inventory) {
    if (inventory[mineralName] > 0) {
      const mineralValue = MINERAL_DATA[mineralName]?.value || 1;
      totalValue += inventory[mineralName] * mineralValue;
    }
  }

  return totalValue;
}

// Initialize UI
export function initializeUI() {
  // Set up screen size slider
  const sizeSlider = document.getElementById('size-slider');
  if (sizeSlider) {
    sizeSlider.value = gameState.getCurrentScreenScale();
    updateScreenSize(gameState.getCurrentScreenScale());
  }

  // Update initial UI state
  updateUI();
}
