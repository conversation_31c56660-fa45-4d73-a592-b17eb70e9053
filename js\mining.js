import { gameState } from './gameState.js';
import { TIER_COLORS, MINING_DISPLAY, GAME_BALANCE, UPGRADES, MINERAL_TIERS, MINERAL_DATA } from './constants.js';
import { calculateArtifactChance, getRandomArtifact, ARTIFACT_DATA } from './artifacts.js';
import { updateUI } from './ui.js';
import { canAddMineralToInventory, addMineralToInventory } from './inventory.js';
import { showNotification } from './utils.js';

// Mining System
export function initializeMiningDisplay() {
  const chunksContainer = document.getElementById('mining-chunks');
  chunksContainer.innerHTML = '';

  const miningChunks = [];
  gameState.setChunksMinedCount(0);
  gameState.setCurrentMiningIndex(0);

  // Get current tier color
  const tierColor = TIER_COLORS[Math.min(gameState.getCurrentTier(), TIER_COLORS.length - 1)];

  // Create 150 chunks (15x10 grid)
  for (let i = 0; i < MINING_DISPLAY.TOTAL_CHUNKS; i++) {
    const chunk = document.createElement('div');
    chunk.className = 'chunk';
    chunk.dataset.index = i;

    // Set tier-based color
    chunk.style.background = tierColor;

    // Randomly place some mineral chunks (10% chance)
    if (Math.random() < MINING_DISPLAY.MINERAL_SPAWN_RATE) {
      chunk.classList.add('mineral');
    }

    chunksContainer.appendChild(chunk);
    miningChunks.push({
      element: chunk,
      mined: false,
      hasMineral: chunk.classList.contains('mineral')
    });
  }

  gameState.setMiningChunks(miningChunks);
  updateMiningDisplay();
}

export function updateMiningDisplay() {
  // Update progress text
  const progressPercent = Math.floor((gameState.getChunksMinedCount() / MINING_DISPLAY.TOTAL_CHUNKS) * 100);
  document.getElementById('depth-progress-text').textContent = `${progressPercent}%`;
  document.getElementById('chunks-mined').textContent = `${gameState.getChunksMinedCount()}/${MINING_DISPLAY.TOTAL_CHUNKS}`;

  // If all chunks are mined, reset the display for next tier
  if (gameState.getChunksMinedCount() >= MINING_DISPLAY.TOTAL_CHUNKS) {
    setTimeout(() => {
      initializeMiningDisplay();
    }, 1000);
  }
}

export function mineChunk() {
  // Find the next chunk to mine in left-to-right order
  if (gameState.getCurrentMiningIndex() >= MINING_DISPLAY.TOTAL_CHUNKS) return;

  const miningChunks = gameState.getMiningChunks();
  const chunkToMine = miningChunks[gameState.getCurrentMiningIndex()];

  if (!chunkToMine || chunkToMine.mined) {
    gameState.incrementCurrentMiningIndex();
    return;
  }

  // Mark as mined
  chunkToMine.mined = true;
  chunkToMine.element.classList.add('mined');
  gameState.incrementChunksMinedCount();
  gameState.incrementCurrentMiningIndex();

  // Create mining particle effect
  createMiningParticle(chunkToMine.element);

  // Check if it had a mineral
  if (chunkToMine.hasMineral) {
    // Add sparkle effect before mining
    setTimeout(() => {
      chunkToMine.element.style.background = 'rgba(255, 215, 0, 0.3)';
    }, 200);
  }

  updateMiningDisplay();
}

export function createMiningParticle(chunkElement) {
  const rect = chunkElement.getBoundingClientRect();
  const particle = document.createElement('div');
  particle.className = 'mining-particle';

  // Position particle at chunk location
  particle.style.left = rect.left + 'px';
  particle.style.top = rect.top + 'px';
  particle.style.setProperty('--tx', (Math.random() - 0.5) * 2);
  particle.style.setProperty('--ty', (Math.random() - 0.5) * 2);

  document.body.appendChild(particle);

  // Remove particle after animation
  setTimeout(() => {
    if (particle.parentNode) {
      particle.parentNode.removeChild(particle);
    }
  }, 1000);
}

export function mine() {
  // Check for active events that halt mining
  const haltingEvent = gameState.getActiveEvents().find(event => event.effect === 'mining_halt');
  if (haltingEvent) {
    return; // Mining is halted
  }

  // Calculate total vein chance
  const totalVeinChance = gameState.getVeinChance();
  const isVein = Math.random() < totalVeinChance;
  const amount = isVein ? 5 : 1;

  // Get current tier minerals
  const currentTier = gameState.getCurrentTier();
  const availableMinerals = MINERAL_TIERS[Math.min(currentTier, MINERAL_TIERS.length - 1)];

  if (!availableMinerals || availableMinerals.length === 0) {
    console.warn('No minerals available for current tier:', currentTier);
    return;
  }

  // Check for artifact discovery first (rare chance)
  const artifactChance = calculateArtifactChance(currentTier, isVein);
  const foundArtifact = Math.random() < artifactChance;

  if (foundArtifact) {
    const randomArtifact = getRandomArtifact(currentTier);
    if (randomArtifact && canAddMineralToInventory(randomArtifact)) {
      addMineralToInventory(randomArtifact, 1);

      // Track discovered artifacts
      if (!gameState.foundArtifacts[randomArtifact]) {
        gameState.foundArtifacts[randomArtifact] = 0;
      }
      gameState.foundArtifacts[randomArtifact]++;

      const artifact = ARTIFACT_DATA[randomArtifact];
      showNotification(`🏺 Rare artifact discovered: ${randomArtifact} (${artifact.rarity})!`, 'success');
    }
  } else {
    // Mine minerals
    for (let i = 0; i < amount; i++) {
      const randomMineral = availableMinerals[Math.floor(Math.random() * availableMinerals.length)];

      // Check if we can add to inventory
      if (canAddMineralToInventory(randomMineral)) {
        addMineralToInventory(randomMineral, 1);
        gameState.incrementMineralsMined();

        // Track discovered minerals
        gameState.mineralsDiscovered.add(randomMineral);

        // Update found minerals count
        if (!gameState.foundMinerals[randomMineral]) {
          gameState.foundMinerals[randomMineral] = 0;
        }
        gameState.foundMinerals[randomMineral]++;
      } else {
        // Inventory full notification (throttled)
        const now = Date.now();
        if (now - gameState.lastInventoryFullWarning > 5000) { // 5 second throttle
          showNotification('Inventory full! Sell minerals or buy more inventory space.', 'warning');
          gameState.lastInventoryFullWarning = now;
        }
        break; // Stop mining if inventory is full
      }
    }
  }

  // Update depth and tier progression
  gameState.addDepth(GAME_BALANCE.DEPTH_PER_MINE);
  gameState.updateDeepestDepth();

  // Check for tier advancement
  const newTier = GAME_BALANCE.DEPTH_TIERS.findIndex(tierDepth => gameState.getCurrentDepth() < tierDepth);
  if (newTier !== -1 && newTier > gameState.getCurrentTier()) {
    gameState.setCurrentTier(newTier);
    showNotification(`Reached Tier ${newTier}! New minerals available!`, 'success');

    // Reset mining display for new tier
    setTimeout(() => {
      initializeMiningDisplay();
    }, 500);
  }

  // Mine a chunk from the visual display
  mineChunk();

  // Update UI
  updateUI();

  if (isVein) {
    gameState.incrementVeinsFound();
    showNotification(`Vein found! Mined ${amount} minerals!`, 'success');
  }
}

export function toggleAutoMining() {
  const autoMining = gameState.isAutoMining();
  gameState.setAutoMining(!autoMining);
  const btn = document.getElementById("auto-btn");

  if (!autoMining) {
    btn.textContent = "⛏️ Stop Mining";
    startMiningInterval();
  } else {
    btn.textContent = "⛏️ Start Mining";
    stopMiningInterval();
  }
}

export function startMiningInterval() {
  stopMiningInterval(); // Clear any existing intervals

  // Calculate base time for current tier (30s + 15s per tier)
  const tierBaseMiningTime = GAME_BALANCE.BASE_MINING_TIME +
    (gameState.getCurrentTier() * GAME_BALANCE.TIER_TIME_INCREASE);

  // Calculate total speed bonus from all sources
  const drillBonus = 1 + (gameState.getDrillLevel() * UPGRADES.DRILL_SPEED_BOOST);
  const totalSpeedMultiplier = gameState.miningSpeed * gameState.prestigeBonus * drillBonus;

  // Apply event effects
  const slowingEvent = gameState.getActiveEvents().find(event => event.effect === 'slow_mining');
  const eventMultiplier = slowingEvent ? slowingEvent.multiplier : 1;

  const intervalTime = (tierBaseMiningTime / totalSpeedMultiplier) / eventMultiplier;

  // Set next mine time
  gameState.nextMineTime = Date.now() + intervalTime;

  // Start timer update
  startTimerUpdate();

  gameState.autoInterval = setInterval(() => {
    mine();
    gameState.nextMineTime = Date.now() + intervalTime; // Reset timer for next mine
  }, intervalTime);
}

export function stopMiningInterval() {
  if (gameState.autoInterval) {
    clearInterval(gameState.autoInterval);
    gameState.autoInterval = null;
  }
  if (gameState.timerUpdateInterval) {
    clearInterval(gameState.timerUpdateInterval);
    gameState.timerUpdateInterval = null;
  }
}

export function startTimerUpdate() {
  if (gameState.timerUpdateInterval) {
    clearInterval(gameState.timerUpdateInterval);
  }

  gameState.timerUpdateInterval = setInterval(() => {
    updateMiningTimer();
  }, 100); // Update every 100ms for smooth countdown
}

export function updateMiningTimer() {
  const timerElement = document.getElementById('timer-countdown');
  const timerContainer = document.getElementById('mining-timer');

  if (!timerElement || !gameState.isAutoMining()) {
    if (timerElement) timerElement.textContent = '--';
    return;
  }

  // Check for active events that affect mining
  const haltingEvent = gameState.getActiveEvents().find(event => event.effect === 'mining_halt');
  const slowingEvent = gameState.getActiveEvents().find(event => event.effect === 'slow_mining');

  if (haltingEvent) {
    timerElement.textContent = 'HALTED';
    timerContainer.classList.add('halted');
    timerContainer.classList.remove('slowed');
    return;
  }

  timerContainer.classList.remove('halted');

  if (slowingEvent) {
    timerContainer.classList.add('slowed');
  } else {
    timerContainer.classList.remove('slowed');
  }

  const now = Date.now();
  const timeRemaining = Math.max(0, gameState.nextMineTime - now);

  if (timeRemaining > 0) {
    const seconds = Math.ceil(timeRemaining / 1000);
    timerElement.textContent = `${seconds}s`;
  } else {
    timerElement.textContent = '0s';
  }
}

export function getCurrentMiningTime() {
  const tierBaseMiningTime = GAME_BALANCE.BASE_MINING_TIME +
    (gameState.getCurrentTier() * GAME_BALANCE.TIER_TIME_INCREASE);
  const drillBonus = 1 + (gameState.getDrillLevel() * UPGRADES.DRILL_SPEED_BOOST);
  const totalSpeedMultiplier = gameState.miningSpeed * gameState.prestigeBonus * drillBonus;

  // Apply event effects
  const slowingEvent = gameState.getActiveEvents().find(event => event.effect === 'slow_mining');
  const eventMultiplier = slowingEvent ? slowingEvent.multiplier : 1;

  return (tierBaseMiningTime / totalSpeedMultiplier) / eventMultiplier;
}
