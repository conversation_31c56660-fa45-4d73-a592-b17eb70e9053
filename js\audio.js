import { gameState } from './gameState.js';

// Audio System Management

// Audio elements
let bgMusic = null;
let miningSound = null;
let sellSound = null;
let upgradeSound = null;

export function initializeAudio() {
  // Get audio elements
  bgMusic = document.getElementById("bg-music");
  miningSound = document.getElementById("mining-sound");
  sellSound = document.getElementById("sell-sound");
  upgradeSound = document.getElementById("upgrade-sound");

  console.log('Initializing audio system...');
  console.log('bgMusic element:', bgMusic);

  if (!bgMusic) {
    console.error('❌ bgMusic element not found!');
    return;
  }

  // Set volume to a comfortable level
  bgMusic.volume = 0.4;
  console.log('Set volume to:', bgMusic.volume);

  // Ensure looping is enabled
  bgMusic.loop = true;
  console.log('Loop enabled:', bgMusic.loop);

  // Try to load the default MP3 file if it exists
  tryLoadDefaultMusic();

  // Handle music loading errors gracefully
  bgMusic.addEventListener('error', function(e) {
    console.error('❌ Background music failed to load:', e);
    console.error('Error details:', e.target.error);
    console.error('Network state:', bgMusic.networkState);
    console.error('Ready state:', bgMusic.readyState);

    // Try to provide helpful error message
    if (e.target.error) {
      switch(e.target.error.code) {
        case e.target.error.MEDIA_ERR_ABORTED:
          console.error('Audio loading was aborted');
          break;
        case e.target.error.MEDIA_ERR_NETWORK:
          console.error('Network error while loading audio');
          break;
        case e.target.error.MEDIA_ERR_DECODE:
          console.error('Audio file is corrupted or unsupported format');
          break;
        case e.target.error.MEDIA_ERR_SRC_NOT_SUPPORTED:
          console.error('Audio file format not supported');
          break;
      }
    }
  });

  // Handle successful loading
  bgMusic.addEventListener('canplaythrough', function() {
    console.log('✅ Background music loaded successfully and ready to loop');
    console.log('Music duration:', bgMusic.duration, 'seconds');
  });

  // Backup looping mechanism in case HTML loop attribute fails
  bgMusic.addEventListener('ended', function() {
    console.log('Music ended event fired');
    if (gameState.isMusicEnabled()) {
      console.log('Restarting music for continuous loop');
      bgMusic.currentTime = 0;
      bgMusic.play().catch(e => console.warn('Failed to restart music:', e));
    }
  });

  // Handle loading state
  bgMusic.addEventListener('loadstart', function() {
    console.log('Started loading background music...');
  });

  // Test if the file can be loaded
  console.log('Music source:', bgMusic.src || bgMusic.currentSrc);
  console.log('Music ready state:', bgMusic.readyState);
}

function tryLoadDefaultMusic() {
  console.log('Attempting to load default music file...');

  // Try different possible paths
  const possiblePaths = [
    'light-contours-ai-213696.mp3',
    './light-contours-ai-213696.mp3',
    '../light-contours-ai-213696.mp3'
  ];

  let pathIndex = 0;

  function tryNextPath() {
    if (pathIndex >= possiblePaths.length) {
      console.log('❌ Default music file not found. Please use the file input to load your MP3.');
      return;
    }

    const path = possiblePaths[pathIndex];
    console.log('Trying path:', path);

    bgMusic.src = path;

    // Set up one-time event listeners
    const onLoad = () => {
      console.log('✅ Default music loaded from:', path);
      bgMusic.removeEventListener('canplaythrough', onLoad);
      bgMusic.removeEventListener('error', onError);
    };

    const onError = () => {
      console.log('❌ Failed to load from:', path);
      bgMusic.removeEventListener('canplaythrough', onLoad);
      bgMusic.removeEventListener('error', onError);
      pathIndex++;
      tryNextPath();
    };

    bgMusic.addEventListener('canplaythrough', onLoad, { once: true });
    bgMusic.addEventListener('error', onError, { once: true });

    bgMusic.load();
  }

  tryNextPath();
}

export function loadMusicFile(input) {
  const file = input.files[0];
  if (!file) return;

  console.log('Loading music file:', file.name);

  if (!file.type.startsWith('audio/')) {
    alert('Please select an audio file (MP3)');
    return;
  }

  // Create object URL for the file
  const url = URL.createObjectURL(file);
  console.log('Created object URL:', url);

  // Set the audio source
  bgMusic.src = url;
  bgMusic.load();

  console.log('✅ Music file loaded successfully:', file.name);
  alert('Music file loaded! You can now enable music.');

  // Clean up previous URL if exists
  if (bgMusic.previousObjectURL) {
    URL.revokeObjectURL(bgMusic.previousObjectURL);
  }
  bgMusic.previousObjectURL = url;
}

export function startMusic() {
  console.log('startMusic called. musicEnabled:', gameState.isMusicEnabled(), 'bgMusic:', bgMusic);

  if (!gameState.isMusicEnabled() || !bgMusic) {
    console.log('Music not enabled or bgMusic not found');
    return;
  }

  // Check if the audio file is loaded
  if (bgMusic.readyState < 2) {
    console.log('Audio not ready, waiting for load...');
    bgMusic.addEventListener('canplay', function() {
      console.log('Audio now ready, attempting to play...');
      attemptPlay();
    }, { once: true });

    // Force load the audio
    bgMusic.load();
    return;
  }

  attemptPlay();

  function attemptPlay() {
    try {
      // Reset to beginning for fresh start
      bgMusic.currentTime = 0;
      console.log('Reset music to beginning');

      // Play the music with looping
      const playPromise = bgMusic.play();
      console.log('Called bgMusic.play(), promise:', playPromise);

      // Handle play promise (required by modern browsers)
      if (playPromise !== undefined) {
        playPromise.then(() => {
          console.log('✅ Background music started successfully and will loop continuously');
        }).catch(error => {
          console.error('❌ Could not start background music:', error);
          console.error('Error name:', error.name);
          console.error('Error message:', error.message);

          // Try alternative approach
          if (error.name === 'NotAllowedError') {
            console.log('🔄 Trying user interaction approach...');
            alert('Click OK to enable music (browser requires user interaction)');
            bgMusic.play().catch(e => console.error('Still failed:', e));
          }
        });
      }
    } catch (error) {
      console.error('❌ Error starting music:', error);
      console.error('Trying to reload audio file...');
      bgMusic.load();
    }
  }
}

export function stopMusic() {
  console.log('stopMusic called. bgMusic:', bgMusic, 'paused:', bgMusic ? bgMusic.paused : 'N/A');

  if (bgMusic && !bgMusic.paused) {
    bgMusic.pause();
    console.log('✅ Background music stopped');
  } else {
    console.log('Music already paused or bgMusic not found');
  }
}

export function toggleMusic() {
  const musicEnabled = !gameState.isMusicEnabled();
  gameState.setMusicEnabled(musicEnabled);
  
  const btn = document.getElementById("music-btn");
  
  if (musicEnabled) {
    startMusic();
    if (btn) btn.textContent = "🔊 Music On";
  } else {
    stopMusic();
    if (btn) btn.textContent = "🔈 Music Off";
  }
}

export function toggleSoundEffects() {
  const soundEffectsEnabled = !gameState.isSoundEffectsEnabled();
  gameState.setSoundEffectsEnabled(soundEffectsEnabled);
  
  const btn = document.getElementById("sfx-btn");
  if (btn) {
    btn.textContent = soundEffectsEnabled ? "🔉 SFX On" : "🔇 SFX Off";
  }
}

export function playMiningSound() {
  if (gameState.isSoundEffectsEnabled() && miningSound) {
    miningSound.currentTime = 0;
    miningSound.play().catch(e => console.warn('Could not play mining sound:', e));
  }
}

export function playSellSound() {
  if (gameState.isSoundEffectsEnabled() && sellSound) {
    sellSound.currentTime = 0;
    sellSound.play().catch(e => console.warn('Could not play sell sound:', e));
  }
}

export function playUpgradeSound() {
  if (gameState.isSoundEffectsEnabled() && upgradeSound) {
    upgradeSound.currentTime = 0;
    upgradeSound.play().catch(e => console.warn('Could not play upgrade sound:', e));
  }
}

// Initialize audio button states
export function updateAudioButtons() {
  const musicBtn = document.getElementById("music-btn");
  const sfxBtn = document.getElementById("sfx-btn");
  
  if (musicBtn) {
    musicBtn.textContent = gameState.isMusicEnabled() ? "🔊 Music On" : "🔈 Music Off";
  }
  
  if (sfxBtn) {
    sfxBtn.textContent = gameState.isSoundEffectsEnabled() ? "🔉 SFX On" : "🔇 SFX Off";
  }
}
